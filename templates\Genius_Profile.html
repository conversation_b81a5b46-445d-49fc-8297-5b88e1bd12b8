<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="GigGenius - Professional freelancer profile and portfolio">
    <title>GigGenius - Profile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --text-light: #ffffff;
            --text-dark: #333333;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --border-radius-sm: 4px;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 20px;
            --transition-fast: 0.2s ease;
            --transition: 0.3s ease;
        }

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: #f8f9fa;
            color: var(--text-dark);
            line-height: 1.6;
            min-height: 100vh;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        ul {
            list-style: none;
        }

        button, input, select, textarea {
            font-family: inherit;
            font-size: inherit;
            outline: none;
        }

        button {
            cursor: pointer;
            border: none;
            background: none;
        }

        img, video {
            max-width: 100%;
            height: auto;
        }

        /* Layout */
        .header-container {
            max-width: 2000px;
            margin: 0 auto;
            background-color: white;
            box-shadow: var(--shadow-sm);
        }

        .body-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(30px);
            box-shadow:
                0 30px 80px rgba(0, 0, 0, 0.15),
                0 15px 40px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border-radius: 32px 32px 0 0;
            border: 1px solid rgba(255, 255, 255, 0.6);
            min-height: calc(100vh - 5rem);
            display: flex;
            flex-direction: column;
            margin-top: 3rem;
            overflow: hidden;
            position: relative;
        }

        .main-content {
            padding: 2rem 1.5rem 2.5rem;
            flex: 1;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
            position: relative;
        }

        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 10% 20%, rgba(0, 74, 173, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 90% 80%, rgba(205, 32, 139, 0.03) 0%, transparent 50%);
            pointer-events: none;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 1rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5rem;
            position: relative;
            z-index: 1000;
            min-height: 5rem;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding-left: 0;
            flex: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            color: var(--primary-pink);
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            color: var(--primary-pink);
        }

        .logo:hover, .logo:active {
            color: var(--primary-blue);
        }

        .logo:hover h1 {
            color: var(--primary-blue);
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
            height: 100%;
            margin: 0;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }
        /* Remove underline from individual nav items */
        .nav-links > a:after,
        .nav-dropbtn:after {
            display: none !important;
        }
        .nav-links > a,
        .nav-links > a:hover,
        .nav-links > a.active,
        .nav-dropbtn,
        .nav-dropbtn:hover,
        .nav-dropbtn.active,
        .nav-dropdown-content a,
        .nav-dropdown-content a:hover {
            text-decoration: none !important;
            border-bottom: none !important;
        }
        .nav-links > a:hover, .nav-links > a.active,
        .nav-dropbtn:hover, .nav-dropbtn.active {
            color: var(--primary-pink);
        }
        .nav-dropdown-content a:after {
            display: none !important;
        }
        .navbar {
            position: relative;
        }
        .nav-links > a:hover, .nav-links > a.active {
            color: var(--primary-pink);
            background-color: transparent;
        }
        .nav-dropbtn:hover, .nav-dropbtn.active {
            color: var(--primary-pink);
            background-color: transparent;
        }

        .nav-dropbtn {
            font-weight: 600;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-dropbtn {
            font-weight: 600;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
            position: relative;
        }

        .nav-dropbtn:hover, .nav-dropbtn.active {
            color: var(--primary-pink);
            background-color: transparent;
            border-bottom: 2px solid var(--primary-pink);
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
        }

        .nav-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 1rem;
        }

        .nav-dropdown-content a:hover {
            background-color: transparent;
            color: var(--primary-pink);
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            padding-right: 1rem;
            justify-content: flex-end;
        }

        /* Search and Auth Styles */
        .search-container {
            display: flex;
            align-items: center;
            margin-right: 1rem;
        }

        .search-bar {
            position: relative;
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 25px;
            padding: 0.4rem 1rem;
            width: 200px;
            transition: all 0.3s ease;
        }

        .search-bar:focus-within {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(0, 74, 173, 0.1);
        }

        .search-bar input {
            border: none;
            background: transparent;
            outline: none;
            flex: 1;
            font-size: 0.9rem;
            color: #333;
        }

        .search-bar input::placeholder {
            color: #6c757d;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            cursor: pointer;
            margin-left: 0.5rem;
        }

        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Notification Styles */
        .notification-container {
            position: relative;
        }

        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: transparent;
        }

        .notification-icon:hover {
            background-color: transparent;
        }

        .notification-icon i {
            font-size: 1.2rem;
            color: var(--primary-blue);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: var(--primary-pink);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
        }

        .notification-dropdown {
            position: absolute;
            top: calc(100% + 10px);
            right: 0;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            width: 380px;
            max-height: 500px;
            overflow: hidden;
            z-index: 1000;
            border: 1px solid rgba(0, 74, 173, 0.08);
            display: none;
        }

        .notification-dropdown.active {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .notification-header {
            padding: 16px 20px 12px;
            border-bottom: 1px solid rgba(0, 74, 173, 0.06);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 700;
            color: var(--primary-blue);
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            position: relative;
        }

        .notification-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            border-radius: 16px 16px 0 0;
        }

        .notification-header-actions {
            font-size: 0.8rem;
            color: var(--primary-pink);
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 8px;
            transition: all 0.2s ease;
            font-weight: 600;
            background: rgba(205, 32, 139, 0.05);
            border: 1px solid rgba(205, 32, 139, 0.1);
        }

        .notification-header-actions:hover {
            background: rgba(205, 32, 139, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(205, 32, 139, 0.15);
        }

        .empty-notifications {
            padding: 40px 20px;
            text-align: center;
            color: #6b7280;
            background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
        }

        .empty-notifications i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.3;
        }

        .empty-notifications h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .empty-notifications p {
            margin: 0;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Profile Dropdown Styles */
        .profile-dropdown {
            position: relative;
        }

        .profile-button {
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid var(--primary-blue);
            transition: all 0.3s ease;
        }

        .profile-button:hover {
            border-color: var(--primary-pink);
            transform: scale(1.05);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-dropdown-content {
            position: absolute;
            top: calc(100% + 10px);
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            min-width: 200px;
            z-index: 1000;
            border: 1px solid rgba(0, 74, 173, 0.08);
            overflow: hidden;
            display: none;
        }

        .profile-dropdown-content.active {
            display: block;
            animation: slideDown 0.3s ease;
        }

        .profile-dropdown-content a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 1px solid rgba(0, 74, 173, 0.05);
        }

        .profile-dropdown-content a:last-child {
            border-bottom: none;
        }

        .profile-dropdown-content a:hover {
            background: rgba(0, 74, 173, 0.05);
            color: var(--primary-pink);
            transform: translateX(4px);
        }

        .profile-dropdown-content a i {
            width: 16px;
            text-align: center;
            opacity: 0.7;
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }

        /* Show dropdown on click */
        .profile-dropdown.active .profile-dropdown-content {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Mobile Responsive Styles */
        @media (max-width: 992px) {
            .navbar-left {
                padding-left: 0;
            }

            .logo img {
                width: 2.4rem;
                height: 2.4rem;
                border-radius: 50%;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                flex-shrink: 0;
            }

            .logo h1 {
                font-size: 1.1rem;
                margin: 0;
                white-space: nowrap;
                font-weight: 600;
                color: var(--primary-pink);
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .nav-links {
                gap: 0.5rem;
            }

            .nav-links a {
                font-size: 0.9rem;
                padding: 0.4rem 0.8rem;
            }

            .nav-dropbtn {
                font-size: 0.9rem;
                padding: 0.4rem 0.8rem;
            }

            .search-bar {
                width: 160px;
                height: 36px;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                height: 4rem;
                padding: 0 0.8rem;
            }

            .logo h1 {
                font-size: 1.2rem;
            }

            .nav-links {
                gap: 0.3rem;
            }

            .nav-links a {
                font-size: 0.8rem;
                padding: 0.3rem 0.6rem;
            }

            .nav-dropbtn {
                font-size: 0.8rem;
                padding: 0.3rem 0.6rem;
            }

            .search-bar {
                width: 140px;
                height: 34px;
            }

            .right-section {
                gap: 0.8rem;
            }

            .body-container {
                margin-top: 1rem;
                border-radius: 16px 16px 0 0;
            }

            .main-content {
                padding: 1.5rem 1rem 2rem;
            }

            .profile-name {
                font-size: 2rem;
            }

            .profile-title {
                font-size: 1.1rem;
            }

            .profile-stats {
                grid-template-columns: 1fr;
                gap: 0.8rem;
            }

            .stat-item {
                padding: 1.2rem 0.8rem;
            }

            .section-header {
                padding: 1.5rem 1.5rem 1rem;
            }

            .section-content {
                padding: 1.2rem;
            }

            .section-title {
                font-size: 1.25rem;
            }
        }

        /* Simplified animations removed */

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Enhanced focus states */
        *:focus {
            outline: 2px solid var(--primary-blue);
            outline-offset: 2px;
        }

        /* Loading animation for images */
        .portfolio-image {
            position: relative;
            overflow: hidden;
        }

        .portfolio-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 2s infinite;
        }





        /* Profile Section */
        .profile-section {
            margin-bottom: 2rem;
            position: relative;
            padding: 1rem 0;
        }

        .profile-section::before {
            content: '';
            position: absolute;
            top: -0.5rem;
            left: -1rem;
            right: -1rem;
            height: 150px;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            opacity: 0.03;
            border-radius: 16px;
            z-index: -1;
        }

        .profile-header {
            margin-bottom: 1.5rem;
            text-align: center;
            padding: 1rem 0;
        }

        .profile-name {
            font-size: 2.8rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 70%, #f093fb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.8rem;
            letter-spacing: -0.02em;
            line-height: 1.1;
            position: relative;
        }

        .profile-title {
            font-size: 1.4rem;
            color: #4a5568;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
            line-height: 1.3;
        }

        .profile-title i {
            color: var(--primary-pink);
            font-size: 1.2rem;
            filter: drop-shadow(0 2px 4px rgba(205, 32, 139, 0.3));
        }



        .profile-row {
            display: flex;
            flex-direction: column;
            gap: 0;
            margin-bottom: 1.5rem;
            align-items: stretch;
        }

        @media (min-width: 1024px) {
            .profile-row {
                flex-direction: row;
                gap: 0;
                align-items: stretch;
            }
        }

        .video-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border-radius: 16px 0 0 16px;
            border: 1px solid rgba(0, 74, 173, 0.1);
            border-right: none;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.04);
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            padding: 1rem;
            display: flex;
            align-items: center;
            margin-bottom: 0;
        }

        .profile-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 0 16px 16px 0;
            border: 1px solid rgba(0, 74, 173, 0.1);
            border-left: none;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.04);
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            padding: 1.8rem;
            margin-bottom: 0;
        }

        .video-card::before, .profile-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .video-card:hover, .profile-card:hover {
            transform: translateY(-6px) scale(1.01);
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(0, 74, 173, 0.1);
        }

        .video-container:hover {
            border-color: rgba(0, 74, 173, 0.25);
            box-shadow:
                0 8px 30px rgba(0, 0, 0, 0.12),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .video-card:hover::before, .profile-card:hover::before {
            opacity: 1;
        }

        @media (min-width: 1024px) {
            .video-card {
                width: 45%;
                border-radius: 16px 0 0 16px;
                border-right: none;
            }

            .profile-card {
                width: 55%;
                border-radius: 0 16px 16px 0;
                border-left: none;
            }
        }

        @media (max-width: 1023px) {
            .video-card {
                border-radius: 16px;
                border: 1px solid rgba(0, 74, 173, 0.1);
                margin-bottom: 0.5rem;
            }

            .profile-card {
                border-radius: 16px;
                border: 1px solid rgba(0, 74, 173, 0.1);
            }
        }

        /* Enhanced Video Container - Professional styling */
        .video-container {
            position: relative;
            width: 100%;
            height: 480px;
            overflow: hidden;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid rgba(0, 74, 173, 0.1);
            border-radius: 16px;
            transition: all 0.3s ease;
            margin: 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);
        }

        .video-container:hover {
            border-color: #004AAD;
            box-shadow: 0 6px 20px rgba(0, 74, 173, 0.15);
            transform: translateY(-3px);
        }

        .video-container video {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 16px;
            min-height: 480px;
        }

        .expand-btn {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            z-index: 2;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.875rem;
        }

        .expand-btn:hover {
            background: rgba(0, 0, 0, 0.7);
        }

        .video-container.expanded {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            padding: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .video-container.expanded video {
            width: 90%;
            height: 90%;
            object-fit: contain;
        }

        /* Profile Content */
        .profile-content {
            padding: 1rem 1.8rem 1.8rem 1.8rem;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .profile-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
            margin-bottom: 1rem;
            padding: 0;
        }

        @media (min-width: 768px) {
            .profile-stats {
                grid-template-columns: repeat(4, 1fr);
                gap: 0.5rem;
            }
        }

        .stat-item {
            text-align: center;
            padding: 1.5rem 1rem;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.9) 100%);
            border-radius: 16px;
            border: 1px solid rgba(0, 74, 173, 0.1);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);
            position: relative;
            overflow: hidden;
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
        }

        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 70%, #f093fb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            display: block;
            line-height: 1.1;
            position: relative;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #4a5568;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            line-height: 1.2;
        }

        .profile-summary {
            margin-bottom: 1rem;
            margin-top: 0;
        }

        .profile-summary h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .profile-summary h3 i {
            color: var(--primary-blue);
        }

        .summary-text {
            color: var(--gray-600);
            margin-bottom: 0.5rem;
        }

        .show-more {
            color: var(--primary-pink);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .show-more:hover {
            text-decoration: underline;
        }

        .edit-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--primary-pink);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            margin-left: 0.75rem;
            background: linear-gradient(135deg, rgba(205, 32, 139, 0.1) 0%, rgba(205, 32, 139, 0.05) 100%);
            border: 1px solid rgba(205, 32, 139, 0.2);
            border-radius: 12px;
            padding: 0.5rem 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
        }

        .edit-btn:hover {
            background: linear-gradient(135deg, rgba(205, 32, 139, 0.15) 0%, rgba(205, 32, 139, 0.1) 100%);
            border-color: rgba(205, 32, 139, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(205, 32, 139, 0.2);
        }

        .profile-fields {
            margin-bottom: 1.5rem;
        }

        .field-group {
            margin-bottom: 1rem;
        }

        .field-group label {
            display: block;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 0.3rem;
            color: #2d3748;
        }

        .field-input {
            width: 100%;
            padding: 0.6rem;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 0.85rem;
            background-color: #f7fafc;
            color: #2d3748;
        }

        .edit-profile-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, var(--primary-pink) 0%, #e91e63 100%);
            color: white;
            border: none;
            border-radius: 16px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            width: 100%;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(212, 27, 140, 0.3);
            position: relative;
            overflow: hidden;
        }

        .edit-profile-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .edit-profile-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(212, 27, 140, 0.4);
        }

        .edit-profile-btn:hover::before {
            left: 100%;
        }

        /* Portfolio and Work History Section */
        .portfolio-section {
            display: flex;
            flex-direction: column;
            gap: 1.2rem;
            margin-top: 1rem;
        }

        .section-row {
            display: flex;
            flex-direction: column;
            gap: 0;
            margin-bottom: 1rem;
        }

        @media (min-width: 1024px) {
            .section-row {
                flex-direction: row;
                gap: 0;
            }
        }

        .section-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(0, 74, 173, 0.1);
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.04);
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            padding: 1.8rem;
            margin-bottom: 0;
        }

        .section-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .section-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .section-card:hover::before {
            opacity: 1;
        }

        @media (min-width: 1024px) {
            .section-card:first-child {
                width: 50%;
                border-radius: 16px 0 0 16px;
                border-right: none;
            }

            .section-card:last-child {
                width: 50%;
                border-radius: 0 16px 16px 0;
                border-left: none;
            }
        }

        @media (max-width: 1023px) {
            .section-card {
                border-radius: 16px;
                border: 1px solid rgba(0, 74, 173, 0.1);
                margin-bottom: 0.5rem;
            }

            .section-card:last-child {
                margin-bottom: 0;
            }
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2.5rem 3rem 2rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.8) 100%);
            backdrop-filter: blur(10px);
            position: relative;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--text-dark);
            letter-spacing: -0.01em;
        }

        .section-title i {
            color: var(--primary-blue);
            font-size: 1.25rem;
            padding: 0.5rem;
            background: rgba(0, 74, 173, 0.1);
            border-radius: 12px;
        }

        .section-content {
            padding: 2rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            position: relative;
        }

        .introduction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.8rem;
        }

        .textarea {
            width: 100%;
            min-height: 120px;
            padding: 0.8rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.85rem;
            resize: vertical;
            margin-bottom: 1rem;
            background-color: #f7fafc;
            color: #2d3748;
            line-height: 1.5;
            background-color: #f9fafb;
        }

        .section-actions {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .arrow-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background-color: #f3f4f6;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s;
        }

        .arrow-btn:hover {
            background-color: #e5e7eb;
            color: #333;
        }

        .add-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, var(--primary-pink) 0%, #e91e63 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 6px 20px rgba(212, 27, 140, 0.25);
            position: relative;
            overflow: hidden;
        }

        .add-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(212, 27, 140, 0.35);
        }

        .add-btn:hover::before {
            left: 100%;
        }

        .portfolio-image {
            width: 100%;
            height: 250px;
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 1.25rem;
            background-color: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
        }

        .portfolio-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .portfolio-image:hover img {
            transform: scale(1.05);
        }

        /* New Portfolio Design */
        .portfolio-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding: 0 1.25rem;
        }

        .portfolio-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }

        .portfolio-actions {
            display: flex;
            gap: 0.5rem;
        }

        .portfolio-add-btn, .portfolio-refresh-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid var(--primary-blue);
            background: white;
            color: var(--primary-blue);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .portfolio-add-btn:hover, .portfolio-refresh-btn:hover {
            background: var(--primary-blue);
            color: white;
        }

        .portfolio-tabs {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
            padding: 0 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .portfolio-tab {
            background: none;
            border: none;
            padding: 0.75rem 0;
            font-size: 1rem;
            font-weight: 500;
            color: var(--gray-600);
            cursor: pointer;
            position: relative;
            transition: color 0.2s;
        }

        .portfolio-tab.active {
            color: var(--text-dark);
        }

        .portfolio-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--text-dark);
        }

        .portfolio-tab:hover {
            color: var(--text-dark);
        }

        .portfolio-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            padding: 0 1.25rem;
            margin-bottom: 2rem;
        }

        .portfolio-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }

        .portfolio-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .portfolio-card-image {
            width: 100%;
            height: 200px;
            overflow: hidden;
        }

        .portfolio-card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .portfolio-card:hover .portfolio-card-image img {
            transform: scale(1.05);
        }

        .portfolio-card-title {
            padding: 1rem;
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: var(--primary-green);
            line-height: 1.4;
        }

        .portfolio-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1.25rem;
        }

        .pagination-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 1px solid #e5e7eb;
            background: white;
            color: var(--gray-600);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 500;
        }

        .pagination-btn:hover {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .pagination-btn.active {
            background: var(--text-dark);
            color: white;
            border-color: var(--text-dark);
        }

        .pagination-dots {
            color: var(--gray-600);
            margin: 0 0.5rem;
        }

        .input-field {
            width: 100%;
            padding: 0.625rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .action-edit-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .action-edit-btn:hover {
            background-color: #1d4ed8;
        }

        .delete-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .delete-btn:hover {
            background-color: #b91c77;
        }

        .work-history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.25rem;
        }

        .work-history-title {
            display: inline-block;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-card {
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            margin-bottom: 1.25rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .certification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .certification-header {
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .certification-title {
            font-size: 1.125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-title i {
            color: var(--primary-pink);
        }

        .certification-content {
            padding: 1.25rem;
        }

        .certification-detail {
            margin-bottom: 0.75rem;
        }

        .certification-detail strong {
            font-weight: 600;
        }

        .certification-description {
            margin-top: 1rem;
        }

        .certification-description h4 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-description h4 i {
            color: var(--primary-blue);
        }

        .certification-description p {
            color: var(--gray-600);
        }

        /* Footer */
        footer {
            background: var(--primary-blue);
            padding: 2rem 5%;
            align-items: center;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 2rem;
            margin-bottom: 2rem;
        }

        @media (min-width: 768px) {
            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .footer-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        .footer-column h3 {
            margin-bottom: 1rem;
            color: var(--text-light);
            font-weight: 600;
        }

        .footer-column a {
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
            opacity: 0.8;
        }

        .footer-column a:hover {
            text-decoration: underline;
            opacity: 1;
        }

        .footer-bottom {
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }
        }

        .footer-bottom p {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 400;
        }

        .social-icons {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            margin: 0;
        }

        .social-icons .bi {
            font-size: 1.2rem;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons a:hover {
            background-color: var(--primary-pink);
            transform: translateY(-3px);
        }

        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal.active {
            display: flex !important;
        }

        /* Delete Confirmation Modal */
        .delete-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .delete-modal.active {
            display: flex !important;
        }

        .delete-modal-content {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            max-width: 400px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .delete-modal-header {
            margin-bottom: 1.5rem;
        }

        .delete-modal-header h3 {
            color: #dc2626;
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .delete-modal-body {
            margin-bottom: 2rem;
        }

        .delete-modal-body p {
            color: #6b7280;
            font-size: 1rem;
            margin: 0;
            line-height: 1.5;
        }

        .delete-modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .delete-modal-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .delete-modal-btn.cancel {
            background: #f3f4f6;
            color: #374151;
        }

        .delete-modal-btn.cancel:hover {
            background: #e5e7eb;
        }

        .delete-modal-btn.confirm {
            background: #dc2626;
            color: white;
        }

        .delete-modal-btn.confirm:hover {
            background: #b91c1c;
        }

        /* Notification Modal Styles */
        .notification-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 3000;
            justify-content: center;
            align-items: center;
        }

        .notification-modal.active {
            display: flex !important;
        }

        .notification-modal-content {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
            animation: notificationSlideIn 0.3s ease-out;
        }

        @keyframes notificationSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .notification-modal-header {
            margin-bottom: 1.5rem;
        }

        .notification-modal-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .notification-modal-icon.success {
            color: #10b981;
        }

        .notification-modal-icon.error {
            color: #ef4444;
        }

        .notification-modal-icon.info {
            color: #3b82f6;
        }

        .notification-modal-icon.warning {
            color: #f59e0b;
        }

        .notification-modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0 0 0.5rem 0;
            color: #1f2937;
        }

        .notification-modal-message {
            color: #6b7280;
            font-size: 1rem;
            line-height: 1.5;
            margin-bottom: 2rem;
        }

        .notification-modal-btn {
            background: #004AAD;
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .notification-modal-btn:hover {
            background: #003a8c;
            transform: translateY(-1px);
        }

        /* Video Action Button Styles */
        .video-action-btn {
            font-family: 'Poppins', sans-serif;
            position: relative;
            overflow: hidden;
        }

        .video-action-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed !important;
            transform: none !important;
        }

        .video-action-btn:disabled:hover {
            background: inherit !important;
            transform: none !important;
            box-shadow: inherit !important;
        }

        .video-action-btn:active {
            transform: translateY(1px) !important;
        }

        .video-action-btn .fas {
            transition: transform 0.2s ease;
        }

        .video-action-btn:hover .fas {
            transform: scale(1.1);
        }

        /* Loading state for save button */
        .video-action-btn.loading {
            pointer-events: none;
        }

        .video-action-btn.loading .fas {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Responsive video container adjustments */
        @media (max-width: 768px) {
            .video-container {
                height: 350px;
            }

            .video-container video {
                min-height: 350px;
            }
        }

        @media (min-width: 769px) and (max-width: 1199px) {
            .video-container {
                height: 420px;
            }

            .video-container video {
                min-height: 420px;
            }
        }

        @media (min-width: 1200px) {
            .video-container {
                height: 520px;
            }

            .video-container video {
                min-height: 520px;
            }
        }

        @media (min-width: 1400px) {
            .video-container {
                height: 580px;
            }

            .video-container video {
                min-height: 580px;
            }
        }

        .modal-content {
            background-color: white;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            overflow-y: auto;
        }

        .modal-header {
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-header-content {
            display: flex;
            align-items: center;
            height: 80px;
            padding: 0 2rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            margin-bottom: 20px;
            text-align: center;
        }

        .modal-textarea {
            width: 100%;
            height: 300px;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 20px;
            outline: none;
            resize: none;
            font-size: 16px;
            font-family: inherit;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .modal-textarea:focus {
            border-color: #8b5cf6;
            box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
        }

        .modal-buttons {
            margin-top: 20px;
            text-align: center;
        }

        .modal-back-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: transparent;
            color: #000;
            text-decoration: underline;
        }

        .modal-back-btn:hover {
            color: #8b5cf6;
        }

        .modal-next-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            background-color: var(--primary-pink);
            color: #fff;
            margin-left: 10px;
        }

        .modal-next-btn:hover {
            background-color: #b91c77;
        }



        /* Portfolio Content Styling */
        .portfolio-card .content-block img {
            max-width: 100% !important;
            height: auto !important;
            border-radius: 4px;
            margin: 0.5rem 0;
        }

        .portfolio-card .content-block video {
            max-width: 100% !important;
            height: auto !important;
            border-radius: 4px;
            margin: 0.5rem 0;
        }

        .portfolio-card .content-block {
            margin-bottom: 0.5rem !important;
            padding: 0.5rem !important;
            border: 1px solid #eee !important;
            border-radius: 4px !important;
        }

        /* Hide any unwanted text content in project content area */
        .project-content p:empty,
        .project-content div:empty,
        .project-content span:empty {
            display: none !important;
        }

        /* Hide text nodes that might contain file paths or HTML code */
        .project-content {
            overflow: hidden;
        }

        /* Hide all text content in project content area - show only images and videos */
        .project-content p,
        .project-content div:not(.content-block),
        .project-content span,
        .project-content h1,
        .project-content h2,
        .project-content h3,
        .project-content h4,
        .project-content h5,
        .project-content h6,
        .project-content b,
        .project-content strong,
        .project-content em,
        .project-content i:not(.fas):not(.far):not(.fab),
        .project-content small,
        .project-content code,
        .project-content pre {
            display: none !important;
        }

        /* Hide all text nodes and HTML entities */
        .project-content {
            font-size: 0 !important;
            line-height: 0 !important;
        }

        /* Show only image and video content blocks */
        .project-content .content-block {
            display: block !important;
            font-size: 0 !important;
        }

        /* Hide text blocks, PDF blocks, and link blocks */
        .project-content .text-block,
        .project-content .pdf-block,
        .project-content .link-block {
            display: none !important;
        }

        /* Hide any text within content blocks except for images and videos */
        .project-content .content-block p,
        .project-content .content-block span,
        .project-content .content-block h1,
        .project-content .content-block h2,
        .project-content .content-block h3,
        .project-content .content-block h4,
        .project-content .content-block h5,
        .project-content .content-block h6,
        .project-content .content-block b,
        .project-content .content-block strong,
        .project-content .content-block em,
        .project-content .content-block i:not(.fas):not(.far):not(.fab),
        .project-content .content-block small,
        .project-content .content-block code,
        .project-content .content-block pre,
        .project-content .content-block div:not(:has(img)):not(:has(video)):not(:has(audio)) {
            display: none !important;
        }

        /* Ensure images and videos are always visible */
        .project-content .content-block img,
        .project-content .content-block video,
        .project-content .content-block audio {
            display: block !important;
            font-size: initial !important;
        }

        /* Hide any stray text nodes that might contain HTML entities */
        .project-content::before,
        .project-content::after {
            content: none !important;
        }

        /* Remove any text content from content blocks */
        .project-content .content-block {
            color: transparent !important;
            text-indent: -9999px !important;
        }

        /* But keep images visible */
        .project-content .content-block img,
        .project-content .content-block video,
        .project-content .content-block audio {
            color: initial !important;
            text-indent: 0 !important;
        }

        .project-content > *:not(img):not(video):not(audio):not(.content-block) {
            display: none !important;
        }

        /* Enhanced Edit Profile Modal */
        @keyframes modalSlideUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Responsive Design for Edit Profile Modal */
        @media (max-width: 768px) {
            #editProfileModal .modal-content {
                max-width: 95% !important;
                margin: 1% auto !important;
            }

            #editProfileModal .modal-body > div:first-child {
                display: block !important;
                gap: 2rem !important;
            }

            #editProfileModal .modal-body > div:first-child > div:first-child {
                margin-bottom: 2rem;
            }

            #editProfileModal .modal-body > div:first-child > div:last-child {
                padding: 1.5rem !important;
            }

            #editProfileModal header {
                padding: 1.5rem !important;
            }

            #editProfileModal .modal-body {
                padding: 1.5rem !important;
            }

            /* Make two-column layouts single column on mobile */
            #editProfileModal div[style*="grid-template-columns: 1fr 1fr"] {
                grid-template-columns: 1fr !important;
            }
        }

        @media (max-width: 480px) {
            #editProfileModal .modal-content {
                max-width: 98% !important;
                margin: 0.5% auto !important;
                border-radius: 12px !important;
            }

            #editProfileModal header {
                padding: 1rem !important;
            }

            #editProfileModal .modal-body {
                padding: 1rem !important;
            }

            #editProfileModal .modal-body > div:first-child > div:last-child {
                padding: 1rem !important;
            }
        }

        .profile-photo-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
        }

        .profile-photo {
            width: 150px;
            height: 150px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            border-radius: 8px;
        }

        .profile-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .upload-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: #f3f4f6;
            color: #333;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .upload-btn:hover {
            background-color: #e5e7eb;
        }

        .upload-note {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .profile-details-section {
            flex: 1;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-group label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--primary-blue);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-control {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid rgba(0, 74, 173, 0.1);
            border-radius: 12px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            color: #333;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 4px rgba(0, 74, 173, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.95);
        }

        .form-control[readonly] {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            cursor: not-allowed;
            border-color: rgba(0, 0, 0, 0.1);
        }

        .form-row {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        @media (min-width: 640px) {
            .form-row {
                flex-direction: row;
            }
        }

        .form-col {
            flex: 1;
        }

        .next-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            width: 100%;
            margin-top: 1rem;
        }

        .next-btn:hover {
            background-color: #b91c77;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                padding: 0 1rem;
            }

            .main-content {
                padding: 1rem;
            }

            .profile-name {
                font-size: 1.5rem;
            }

            .profile-title {
                font-size: 1rem;
            }

            .modal-content {
                width: 95%;
                margin: 1rem;
            }

            .modal-body {
                padding: 1rem;
            }

            .profile-form {
                flex-direction: column;
            }
        }

        /* Premium Text Styling */
        .field-label {
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.95rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .field-value {
            color: var(--gray-800);
            font-size: 1.1rem;
            line-height: 1.6;
            font-weight: 500;
        }

        /* Simplified animations */

        /* Utility Classes */
        .hidden {
            display: none;
        }

        .flex {
            display: flex;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .gap-4 {
            gap: 1rem;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .mb-6 {
            margin-bottom: 1.5rem;
        }

        .text-center {
            text-align: center;
        }

        .font-bold {
            font-weight: bold;
        }

        .font-semibold {
            font-weight: 600;
        }

        .text-sm {
            font-size: 0.875rem;
        }

        .text-lg {
            font-size: 1.125rem;
        }

        .text-xl {
            font-size: 1.25rem;
        }

        .text-2xl {
            font-size: 1.5rem;
        }

        .text-3xl {
            font-size: 1.875rem;
        }

        .w-full {
            width: 100%;
        }

        .h-full {
            height: 100%;
        }

        .rounded {
            border-radius: 0.25rem;
        }

        .rounded-lg {
            border-radius: 0.5rem;
        }

        .shadow-sm {
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .shadow-md {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .transition-all {
            transition: all 0.3s ease;
        }

        .hover\:shadow-md:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .hover\:transform:hover {
            transform: translateY(-2px);
        }

        /* Star rating */
        .star-rating {
            color: #FBBF24;
        }

        /* Toggle switch */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #22C55E;
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
            transform: translateX(20px);
        }

        /* Work History Styles */
        .work-history-summary {
            background-color: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }

        .work-history-summary h3 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .work-history-summary p {
            color: #374151;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .work-history-summary .show-more-btn {
            color: var(--primary-blue);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
        }

        .work-history-summary .ai-note {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.75rem;
        }

        .skills-section {
            margin-bottom: 1.5rem;
        }

        .skills-section h3 {
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .skill-tag {
            background-color: #f3f4f6;
            color: #374151;
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
        }

        .job-tabs {
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 1.5rem;
        }

        .job-tabs .tab-list {
            display: flex;
            gap: 1.5rem;
        }

        .job-tab {
            background: none;
            border: none;
            padding: 0.5rem 0;
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            position: relative;
            transition: color 0.2s;
        }

        .job-tab.active {
            color: #000;
        }

        .job-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: #000;
        }

        .job-tab:hover {
            color: #000;
        }

        .job-list {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .job-item {
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 1.5rem;
        }

        .job-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.25rem;
        }

        .job-title {
            color: var(--primary-blue);
            font-weight: 500;
            margin: 0;
        }

        .job-menu-btn {
            background: none;
            border: none;
            color: #6b7280;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .job-menu-btn:hover {
            background-color: #f3f4f6;
        }

        .job-rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
        }

        .job-rating .stars {
            display: flex;
            color: #FBBF24;
        }

        .job-rating .rating-score {
            font-weight: 500;
        }

        .job-rating .separator {
            color: #6b7280;
        }

        .job-date {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.75rem;
        }

        .job-feedback {
            font-size: 0.875rem;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        .job-earnings {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .job-total {
            font-weight: 500;
        }

        .job-rate, .job-hours {
            font-size: 0.875rem;
            color: #6b7280;
        }



        /* Simplified notification styles */
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="navbar-left">
                <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                    <div class="logo">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                        <h1>GigGenius</h1>
                    </div>
                </a>

                <div class="nav-links" id="navLinks">
                    <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                    <a href="#" id="myApplicationsLink">My Applications</a>

                    <!-- Contracts Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Contracts
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Log Hours</a>
                            <a href="{{ url_for('landing_page') }}">Work Diary</a>
                        </div>
                    </div>

                    <!-- Earnings Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Earnings
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Billings and Earnings</a>
                            <a href="{{ url_for('landing_page') }}">Withdraw Earnings</a>
                            <a href="{{ url_for('landing_page') }}">Tax Info</a>
                        </div>
                    </div>

                    <a href="{{ url_for('messages') }}">Messages</a>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-bar">
                        <input type="text" id="searchInput" placeholder="Search..." onkeypress="if(event.key==='Enter') performSearch()">
                        <i class="fas fa-search icon" onclick="performSearch()"></i>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-container">
                        <div class="notification-icon" id="notification-bell">
                            <i class="fas fa-bell"></i>
                            <span id="notification-count" class="notification-badge" style="display: none;">0</span>
                        </div>
                        <div class="notification-dropdown">
                            <div class="notification-header">
                                <span>Notifications</span>
                                <span class="notification-header-actions" id="mark-all-read">Mark all as read</span>
                            </div>
                            <div id="notification-list">
                                <!-- Notifications will be loaded here -->
                            </div>
                            <div id="empty-notifications" class="empty-notifications" style="display: none;">
                                <i class="far fa-bell-slash"></i>
                                <h3>No notifications yet</h3>
                                <p>You'll see application updates and important notifications here</p>
                            </div>
                        </div>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img id="navProfilePhoto" src="/api/profile-photo/genius/{{ genius.id }}" alt="Profile Picture" onerror="this.src='/static/img/default-avatar.png'">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('genius_profile') }}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-cog"></i> Account Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('logout') }}" class="logout-option" style="color: #dc3545 !important; font-weight: bold;">
                                <i class="fas fa-sign-out-alt"></i> Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>




    <!-- Main Content -->
    <div class="body-container">
        <!-- Main Content -->
        <main class="main-content">
            <!-- Profile Section -->
            <section class="profile-section">
                <!-- Name and Title above video -->
                <div class="profile-header">
                    <h1 class="profile-name" id="profileName">{{ genius.first_name }} {{ genius.last_name }}</h1>
                    <p class="profile-title">
                        <i class="fas fa-briefcase"></i>
                        {% if genius.position %}
                            <span id="profilePosition">{{ genius.position }}</span>
                        {% endif %}
                    </p>
                </div>
                
                <div class="profile-row">
                    <!-- Video Card -->
                    <div class="video-card">
                        <div class="video-container" id="videoContainer">
                            <!-- Video Upload Placeholder (shown when no video) -->
                            <div class="video-placeholder" id="videoPlaceholder" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%); border: 2px dashed #e5e7eb; border-radius: 16px; margin: 0; padding: 0.5rem; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.background='linear-gradient(135deg, #f1f3f4 0%, #e5e7eb 100%)'; this.style.borderColor='#004AAD'; this.style.transform='scale(1.01)'" onmouseout="this.style.background='linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%)'; this.style.borderColor='#e5e7eb'; this.style.transform='scale(1)'">
                                <div style="text-align: center; color: #6c757d; position: relative; z-index: 2;">
                                    <div style="background: rgba(0, 74, 173, 0.1); width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem auto;">
                                        <i class="fas fa-video" style="font-size: 1.5rem; color: #004AAD;"></i>
                                    </div>
                                    <h3 style="margin: 0 0 0.5rem 0; font-size: 1.1rem; font-weight: 600; color: #333;">Add a video introduction</h3>
                                    <p style="margin: 0 0 1rem 0; font-size: 0.85rem; opacity: 0.7; line-height: 1.4;">Show clients who you are with a personal video</p>
                                    <button class="upload-video-btn" id="uploadVideoBtn" style="background: linear-gradient(135deg, #004AAD, #0066cc); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 12px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3);" onmouseover="this.style.background='linear-gradient(135deg, #CD208B, #e91e63)'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(205, 32, 139, 0.4)'" onmouseout="this.style.background='linear-gradient(135deg, #004AAD, #0066cc)'; this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0, 74, 173, 0.3)'">
                                        <i class="fas fa-upload"></i> Upload Video
                                    </button>
                                    <input type="file" id="videoUpload" accept="video/*" style="display: none;">
                                </div>
                            </div>

                            <!-- Video Preview (shown when video is selected but not saved) -->
                            <div class="video-preview" id="videoPreview" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: none;">
                                <video id="previewVideo" controls style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 100%; height: 100%; object-fit: cover;">
                                    <source src="#" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                                <div style="position: absolute; bottom: 15px; right: 15px; display: flex; gap: 0.75rem;">
                                    <button id="cancelVideoBtn" class="video-action-btn cancel-btn" style="background: #f8f9fa; color: #6c757d; border: 2px solid #dee2e6; padding: 0.75rem 1.25rem; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 0.9rem; transition: all 0.3s ease; display: flex; align-items: center; gap: 0.5rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.background='#e9ecef'; this.style.borderColor='#adb5bd'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.15)'" onmouseout="this.style.background='#f8f9fa'; this.style.borderColor='#dee2e6'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'">
                                        <i class="fas fa-times"></i> Cancel
                                    </button>
                                    <button id="saveVideoBtn" class="video-action-btn save-btn" style="background: linear-gradient(135deg, #004AAD 0%, #0056c7 100%); color: white; border: none; padding: 0.75rem 1.25rem; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 0.9rem; transition: all 0.3s ease; display: flex; align-items: center; gap: 0.5rem; box-shadow: 0 2px 8px rgba(0, 74, 173, 0.3);" onmouseover="this.style.background='linear-gradient(135deg, #003a8c 0%, #0047b3 100%)'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(0, 74, 173, 0.4)'" onmouseout="this.style.background='linear-gradient(135deg, #004AAD 0%, #0056c7 100%)'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(0, 74, 173, 0.3)'">
                                        <i class="fas fa-save"></i> Save Video
                                    </button>
                                </div>
                            </div>

                            <!-- Video Player (shown when video exists) -->
                            <div class="video-player" id="videoPlayer" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: none;">
                                <video id="profileVideo" controls poster="https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=450&fit=crop" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 100%; height: 100%; object-fit: cover;">
                                    <source src="#" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>

                                <!-- Video Controls -->
                                <div class="video-controls" style="position: absolute; top: 10px; right: 10px; display: flex; gap: 0.5rem;">
                                    <button class="video-control-btn" id="deleteVideoBtn" style="background: rgba(220, 53, 69, 0.9); color: white; border: none; padding: 0.5rem; border-radius: 4px; cursor: pointer;" title="Delete Video">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button class="video-control-btn" id="replaceVideoBtn" style="background: rgba(40, 167, 69, 0.9); color: white; border: none; padding: 0.5rem; border-radius: 4px; cursor: pointer;" title="Replace Video">
                                        <i class="fas fa-upload"></i>
                                    </button>
                                </div>

                                <!-- Fullscreen Button -->
                                <button class="expand-btn" id="expandBtn" aria-label="Expand video" style="position: absolute; bottom: 10px; right: 10px; background: rgba(0, 0, 0, 0.7); color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
                                    <i class="fas fa-expand"></i> Fullscreen
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Card -->
                    <div class="profile-card">
                        <div class="profile-content">
                            <!-- Stats - Updated to include 4 items -->
                            <div class="profile-stats">
                                <div class="stat-item">
                                    <div class="stat-value" id="hourlyRate">
                                        {% if genius.hourly_rate %}
                                            ${{ genius.hourly_rate }}
                                        {% else %}
                                            <span style="color:#bbb;">No rate set</span>
                                        {% endif %}
                                    </div>
                                    <div class="stat-label">Hourly Rate</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="totalEarnings">$0</div>
                                    <div class="stat-label">Total Earnings</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="hired">0</div>
                                    <div class="stat-label">Hired</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="completedJobs">0</div>
                                    <div class="stat-label">Completed</div>
                                </div>
                            </div>

                            <!-- Professional Summary -->
                            <div class="profile-summary">
                                <div class="flex items-center justify-between mb-4">
                                    <h3><i class="fas fa-user-circle"></i> Professional Summary</h3>
                                    <button class="edit-btn" id="editSummaryBtn">
                                        <i class="fas fa-edit"></i>
                                        <span>Edit</span>
                                    </button>
                                </div>
                                <p class="summary-text" id="summaryText">
                                    {% if genius.professional_sum %}
                                        {{ genius.professional_sum }}
                                    {% else %}
                                        <span style="color: #6b7280; font-style: italic;">
                                            Add a professional summary to showcase your skills and experience to potential clients.
                                            Click the Edit button to get started.
                                        </span>
                                    {% endif %}
                                </p>
                                <span class="show-more hidden" id="showMore"><i class="fas fa-plus-circle"></i> Show More</span>
                            </div>

                            <!-- Profile Fields -->
                            <div class="profile-fields">
                                <div class="field-group">
                                    <label>Availability</label>
                                    <input type="text" value="{% if genius.availability == 'fulltime' %}Full-Time{% elif genius.availability == 'parttime' %}Part-Time{% else %}{{ genius.availability if genius.availability else '' }}{% endif %}" readonly class="field-input" id="displayAvailability">
                                </div>
                                <div class="field-group">
                                    <label>Language</label>
                                    <input type="text" value="{{ genius.language if genius.language else 'English' }}" readonly class="field-input" id="displayLanguage">
                                </div>
                                <div class="field-group">
                                    <label>Country</label>
                                    <input type="text" value="{{ genius.country if genius.country else '' }}" readonly class="field-input" id="displayCountryField">
                                </div>
                            </div>

                            <!-- Edit Profile Button -->
                            <button class="edit-profile-btn" id="editProfileBtn">
                                <i class="fas fa-user-edit"></i>
                                <span>Edit Profile</span>
                            </button>

                            <!-- Test Modal Button (for debugging) -->
                            <button onclick="testModal()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                Test Modal
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Introduction/Portfolio and Work History Section -->
            <section class="portfolio-section">
                <div class="section-row">
                    <!-- Introduction and Portfolio -->
                    <div class="section-card">
                        <!-- Introduction -->
                        <div class="section-content">
                            <div class="introduction-header">
                                <h2 class="section-title" id="introduction"><i class="fas fa-info-circle"></i> Introduction</h2>
                                <button class="edit-btn" id="editIntroductionBtn">
                                    <i class="fas fa-edit"></i>
                                    <span>Edit</span>
                                </button>
                            </div>
                            <textarea class="textarea" id="introductionDisplay" placeholder="Write your introduction not less than 300 words..." readonly>{{ genius.introduction if genius.introduction else '' }}</textarea>
                        </div>

                        <!-- Portfolio Header -->
                        <div class="portfolio-header">
                            <h2 class="portfolio-title">Portfolio</h2>
                            <div class="portfolio-actions">
                                <button class="portfolio-add-btn" id="portfolioAddBtn">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="portfolio-refresh-btn">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Portfolio Tabs -->
                        <div class="portfolio-tabs">
                            <button class="portfolio-tab active" data-tab="published">Published</button>
                            <button class="portfolio-tab" data-tab="drafts">Drafts</button>
                        </div>

                        <!-- Enhanced Add Portfolio Button -->
                        <div style="text-align: center; margin: 1.5rem 0;">
                            <button onclick="openAddPortfolioModal()" style="background: linear-gradient(135deg, #004AAD 0%, #0066cc 100%); color: white; border: none; padding: 1rem 2rem; border-radius: 10px; cursor: pointer; font-weight: 600; font-size: 1rem; font-family: 'Poppins', sans-serif; transition: all 0.3s; display: inline-flex; align-items: center; gap: 0.75rem; box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3); position: relative; overflow: hidden;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(0, 74, 173, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0, 74, 173, 0.3)'">
                                <div style="background: rgba(255,255,255,0.2); padding: 0.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-plus" style="font-size: 0.9rem;"></i>
                                </div>
                                <span>Add New Project</span>
                                <div style="position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); transition: left 0.5s;"></div>
                            </button>
                        </div>

                    <!-- Portfolio Grid -->
                    <div class="portfolio-grid" id="portfolioGrid">
                        <!-- Published Projects (shown by default) -->
                        <div class="portfolio-content" id="publishedContent">
                            {% if genius.portfolio and genius.portfolio.published and genius.portfolio.published|length > 0 %}
                                {% for project in genius.portfolio.published %}
                                    <div class="portfolio-card" data-project-id="{{ project.id }}" style="border: 1px solid #ddd; border-radius: 8px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; transition: transform 0.2s, box-shadow 0.2s; position: relative; margin-bottom: 1rem;">
                                        <!-- Delete Button -->
                                        <button class="delete-portfolio-btn" data-project-id="{{ project.id }}" style="position: absolute; top: 10px; right: 10px; background: rgba(255, 68, 68, 0.9); color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-size: 0.8rem; z-index: 9999; display: flex; align-items: center; justify-content: center; transition: all 0.2s; opacity: 0.8; pointer-events: auto;" onmouseover="this.style.opacity='1'; this.style.transform='scale(1.1)'" onmouseout="this.style.opacity='0.8'; this.style.transform='scale(1)'" onclick="event.stopPropagation(); deletePortfolio({{ project.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <!-- Edit Button -->
                                        <button class="edit-portfolio-btn" data-project-id="{{ project.id }}" style="position: absolute; top: 10px; right: 50px; background: rgba(0, 74, 173, 0.9); color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-size: 0.8rem; z-index: 9999; display: flex; align-items: center; justify-content: center; transition: all 0.2s; opacity: 0.8; pointer-events: auto;" onmouseover="this.style.opacity='1'; this.style.transform='scale(1.1)'" onmouseout="this.style.opacity='0.8'; this.style.transform='scale(1)'" onclick="event.stopPropagation(); editPortfolio({{ project.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <!-- Project Header -->
                                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1.5rem; text-align: center;">
                                            <h3 style="margin: 0; font-size: 1.2rem; font-weight: 600;">{{ project.title }}</h3>
                                            <p style="margin: 0.5rem 0 0 0; opacity: 0.9; font-size: 0.9rem;">{{ project.project_type.replace('_', ' ').title() if project.project_type else 'Project' }}</p>
                                        </div>
                                        <!-- Project Content -->
                                        <div style="padding: 1.5rem;">
                                            {% if project.description %}
                                                <p style="margin: 0 0 1rem 0; color: #666; line-height: 1.5;">{{ project.description }}</p>
                                            {% endif %}
                                            {% if project.technologies %}
                                                <div style="margin-bottom: 1rem;">
                                                    <strong style="color: #004AAD; font-size: 0.9rem;">Technologies:</strong>
                                                    <p style="margin: 0.25rem 0 0 0; color: #666; font-size: 0.9rem;">{{ project.technologies }}</p>
                                                </div>
                                            {% endif %}
                                            <!-- Project Link -->
                                            <div style="text-align: center; margin-top: 1rem;">
                                                <a href="{{ project.project_url }}" target="_blank" style="display: inline-flex; align-items: center; background: #004AAD; color: white; text-decoration: none; padding: 0.75rem 1.5rem; border-radius: 6px; font-weight: 500; transition: background 0.2s;" onmouseover="this.style.background='#003a8c'" onmouseout="this.style.background='#004AAD'">
                                                    <i class="fas fa-external-link-alt" style="margin-right: 0.5rem;"></i>
                                                    View Project
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div style="padding:2rem;text-align:center;color:#888;">No published projects yet. Click + to add your first project.</div>
                            {% endif %}
                        </div>

                        <!-- Draft Projects (hidden by default) -->
                        <div class="portfolio-content" id="draftsContent" style="display: none;">
                            {% if genius.portfolio and genius.portfolio.drafts and genius.portfolio.drafts|length > 0 %}
                                {% for project in genius.portfolio.drafts %}
                                    <div class="portfolio-card clickable-card" data-project-id="{{ project.id }}" data-project-title="{{ project.project_title }}" data-project-role="{{ project.project_role or '' }}" data-project-description="{{ project.project_description }}" data-project-content="{{ project.project_content or '' }}" data-project-skills="{{ project.skills_and_deliverables or '' }}" data-related-job="{{ project.related_giggenius_job or '' }}" data-has-image="{{ 'true' if project.project_image_filename else 'false' }}" data-status="draft" style="border: 1px solid #ddd; border-radius: 8px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; cursor: pointer; transition: transform 0.2s, box-shadow 0.2s; position: relative;">
                                        <!-- Delete Button -->
                                        <button class="delete-portfolio-btn" data-project-id="{{ project.id }}" style="position: absolute; top: 10px; right: 10px; background: rgba(255, 68, 68, 0.9); color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-size: 0.8rem; z-index: 10; display: flex; align-items: center; justify-content: center; transition: all 0.2s; opacity: 0.8;" onmouseover="this.style.opacity='1'; this.style.transform='scale(1.1)'" onmouseout="this.style.opacity='0.8'; this.style.transform='scale(1)'" onclick="event.stopPropagation(); deletePortfolio({{ project.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <!-- Project Image -->
                                        <div style="width: 100%; height: 150px; overflow: hidden; position: relative;">
                                            {% if project.project_image_filename %}
                                                <img src="{{ url_for('api_portfolio_image', project_id=project.id) }}" alt="{{ project.project_title }}" style="width: 100%; height: 100%; object-fit: cover;">
                                            {% else %}
                                                <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #f57c00 0%, #ff9800 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                                                    <i class="fas fa-image"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <!-- Project Title -->
                                        <div class="portfolio-card-content" style="padding: 1rem; text-align: center;">
                                            <h3 style="margin: 0 0 0.5rem 0; font-size: 1.1rem; font-weight: 600; color: #004AAD;">{{ project.project_title }}</h3>
                                            <span style="background: #ffecb3; color: #f57c00; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.7rem;">Draft</span>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div style="padding:2rem;text-align:center;color:#888;">No draft projects yet.</div>
                            {% endif %}
                        </div>
                    </div>

                        <!-- Portfolio Pagination (keep or remove as needed) -->
                        <div class="portfolio-pagination">
                            <button class="pagination-btn prev">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="pagination-btn active">1</button>
                            <button class="pagination-btn">2</button>
                            <button class="pagination-btn">3</button>
                            <button class="pagination-btn">4</button>
                            <span class="pagination-dots">...</span>
                            <button class="pagination-btn next">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Work History -->
                    <div class="section-card">
                        <div class="section-content">
                            <h2 class="text-lg font-semibold mb-6">Work History</h2>

                            <!-- Work History Summary -->
                            <div class="work-history-summary">
                                {% if genius.work_history and genius.work_history|length > 0 %}
                                    {# Render work history summary dynamically #}
                                {% else %}
                                    <p style="color:#888;">No work history yet.</p>
                                {% endif %}
                            </div>

                            <!-- Skills Used -->
                            <div class="skills-section">
                                <h3>Skills used in past work</h3>
                                <div class="skills-list">
                                    <span class="skill-tag">JavaScript</span>
                                    <span class="skill-tag">React</span>
                                    <span class="skill-tag">Node.js</span>
                                    <span class="skill-tag">Python</span>
                                    <span class="skill-tag">Digital Marketing</span>
                                    <span class="skill-tag">SEO</span>
                                    <span class="skill-tag">UI/UX Design</span>
                                    <span class="skill-tag">Database Design</span>
                                </div>
                            </div>

                            <!-- Job Tabs -->
                            <div class="job-tabs">
                                <div class="tab-list">
                                    <button class="job-tab active" data-tab="completed">Completed jobs (12)</button>
                                    <button class="job-tab" data-tab="in-progress">In progress (3)</button>
                                </div>
                            </div>

                            <!-- Job List -->
                            <div class="job-list">
                                {% if genius.jobs and genius.jobs|length > 0 %}
                                    {% for job in genius.jobs %}
                                        <div class="job-item">
                                            <!-- Render job info here -->
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div style="padding:2rem;text-align:center;color:#888;">No jobs to display yet.</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>



    <!-- Modal for Professional Summary -->
    <div class="modal" id="professionalSummaryModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Professional Summary *</h2>
                <textarea class="modal-textarea" id="professionalSummaryTextarea" placeholder="Write something..."></textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="summaryBackBtn">Back</button>
                    <button class="modal-next-btn" id="summaryNextBtn">Next</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Introduction -->
    <div class="modal" id="introductionModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Introduction *</h2>
                <textarea class="modal-textarea" id="introductionTextarea" placeholder="Write something...">{{ genius.introduction or '' }}</textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="introBackBtn">Back</button>
                    <button class="modal-next-btn" id="introNextBtn">Next</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Premium Enhanced Modal for Edit Profile -->
    <div class="modal" id="editProfileModal">
        <div class="modal-content" style="max-width: 1200px; width: 95%; border-radius: 28px; box-shadow: 0 40px 120px rgba(0,0,0,0.2), 0 20px 60px rgba(0,0,0,0.1); backdrop-filter: blur(20px); animation: modalSlideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1); border: 1px solid rgba(255,255,255,0.3);">
            <!-- Simple Clean Header -->
            <header class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 28px 28px 0 0; position: relative;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <!-- Simple Logo & Title -->
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo" style="width: 40px; height: 40px; filter: brightness(0) invert(1);">
                        <div>
                            <h2 style="margin: 0; font-size: 1.8rem; font-weight: 700; font-family: 'Poppins', sans-serif;">Edit Profile</h2>
                            <p style="margin: 0; opacity: 0.9; font-size: 0.9rem;">Update your information</p>
                        </div>
                    </div>

                    <!-- Simple Close Button -->
                    <button onclick="document.getElementById('editProfileModal').classList.remove('active'); document.body.style.overflow = 'auto';" style="background: rgba(255,255,255,0.2); border: none; color: white; font-size: 1.2rem; cursor: pointer; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </header>

            <!-- Premium Main Content -->
            <div class="modal-body" style="padding: 3.5rem; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%); position: relative;">
                <!-- Subtle Background Pattern -->
                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 30% 70%, rgba(102, 126, 234, 0.03) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(240, 147, 251, 0.03) 0%, transparent 50%); pointer-events: none;"></div>

                <!-- Enhanced Progress Indicator -->
                <div style="margin-bottom: 3rem; position: relative; z-index: 1;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                        <h3 style="margin: 0; font-size: 1.5rem; font-weight: 700; color: #1e293b; font-family: 'Poppins', sans-serif; background: linear-gradient(135deg, #1e293b 0%, #475569 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Profile Information</h3>
                        <span style="background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); color: white; padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 0.9rem; font-weight: 600; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3); backdrop-filter: blur(10px);">Complete your profile</span>
                    </div>
                    <div style="background: rgba(229, 231, 235, 0.8); height: 8px; border-radius: 4px; overflow: hidden; backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.3);">
                        <div style="background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%); height: 100%; width: 85%; border-radius: 4px; transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);"></div>
                    </div>
                </div>

                <!-- Enhanced Profile Form -->
                <div style="display: grid; grid-template-columns: 300px 1fr; gap: 3rem; align-items: start;">
                    <!-- Enhanced Profile Photo Section -->
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 1.5rem; background: white; padding: 2rem; border-radius: 16px; box-shadow: 0 8px 25px rgba(0,0,0,0.08); border: 1px solid rgba(0,0,0,0.05);">
                        <div style="position: relative;">
                            <div style="width: 180px; height: 180px; border-radius: 50%; overflow: hidden; border: 4px solid #667eea; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); position: relative;">
                                <img id="currentProfilePhoto" src="/api/profile-photo/genius/{{ genius.id }}" alt="Current Profile Photo" onerror="this.src='/static/img/default-avatar.png'" style="width: 100%; height: 100%; object-fit: cover;">
                            </div>
                            <!-- Photo overlay -->
                            <div style="position: absolute; bottom: 10px; right: 10px; background: #667eea; color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4); transition: all 0.2s;" onclick="document.getElementById('profile-upload').click();">
                                <i class="fas fa-camera" style="font-size: 1rem;"></i>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <label for="profile-upload" style="display: inline-flex; align-items: center; gap: 0.75rem; padding: 0.75rem 1.5rem; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 25px; font-size: 0.9rem; font-weight: 500; cursor: pointer; transition: all 0.2s; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(102, 126, 234, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(102, 126, 234, 0.3)'">
                                <i class="fas fa-camera"></i>
                                <span>Change Photo</span>
                            </label>
                            <input type="file" id="profile-upload" accept="image/*" style="display: none;">
                            <p style="margin: 1rem 0 0 0; font-size: 0.8rem; color: #666; opacity: 0.8;">Maximum of 2MB</p>
                        </div>
                    </div>

                    <!-- Enhanced Profile Details Section -->
                    <div style="background: white; padding: 2.5rem; border-radius: 16px; box-shadow: 0 8px 25px rgba(0,0,0,0.08); border: 1px solid rgba(0,0,0,0.05);">
                        <h2 style="margin: 0 0 2rem 0; font-size: 1.4rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif; display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-user-edit" style="color: #667eea; font-size: 1.2rem;"></i>
                            Edit your profile
                        </h2>

                        <!-- Enhanced Form Fields -->
                        <div style="display: grid; gap: 1.5rem;">
                            <!-- Email Field -->
                            <div style="position: relative;">
                                <label for="email" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-envelope" style="font-size: 0.8rem;"></i>
                                    Email
                                </label>
                                <input type="email" id="email" value="{{ genius.email or '' }}" readonly style="width: 100%; padding: 1rem 1.25rem; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 1rem; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); color: #666; cursor: not-allowed; transition: all 0.3s;">
                            </div>

                            <!-- Mobile Field -->
                            <div style="position: relative;">
                                <label for="mobile" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-phone" style="font-size: 0.8rem;"></i>
                                    Mobile No.
                                </label>
                                <input type="tel" id="mobile" value="{{ genius.mobile or '' }}" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'">
                            </div>

                            <!-- Position Field -->
                            <div style="position: relative;">
                                <label for="position" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-briefcase" style="font-size: 0.8rem;"></i>
                                    Position
                                </label>
                                <input type="text" id="position" value="{{ genius.position or '' }}" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'">
                            </div>

                            <!-- Two Column Layout for Expertise and Rate -->
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem;">
                                <!-- Expertise Level -->
                                <div style="position: relative;">
                                    <label for="expertise" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-star" style="font-size: 0.8rem;"></i>
                                        Expertise Level
                                    </label>
                                    <select id="expertise" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); cursor: pointer;" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'">
                                        <option value="Expert" {% if genius.expertise == 'Expert' %}selected{% endif %}>Expert</option>
                                        <option value="Intermediate" {% if genius.expertise == 'Intermediate' %}selected{% endif %}>Intermediate</option>
                                        <option value="Beginner" {% if genius.expertise == 'Beginner' %}selected{% endif %}>Beginner</option>
                                    </select>
                                </div>

                                <!-- Rate per Hour -->
                                <div style="position: relative;">
                                    <label for="rate" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-dollar-sign" style="font-size: 0.8rem;"></i>
                                        Rate per Hour (USD)
                                    </label>
                                    <input type="number" id="rate" value="{{ genius.hourly_rate or '' }}" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'" placeholder="e.g. 25">
                                </div>
                            </div>

                            <!-- Availability Field -->
                            <div style="position: relative;">
                                <label for="availability" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-clock" style="font-size: 0.8rem;"></i>
                                    Availability
                                </label>
                                <select id="availability" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); cursor: pointer;" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'">
                                    <option value="fulltime" {% if genius.availability == 'fulltime' %}selected{% endif %}>Full-Time</option>
                                    <option value="parttime" {% if genius.availability == 'parttime' %}selected{% endif %}>Part-Time</option>
                                </select>
                            </div>

                            <!-- Two Column Layout for Country and Language -->
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem;">
                                <!-- Country Field -->
                                <div style="position: relative;">
                                    <label for="country" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-globe" style="font-size: 0.8rem;"></i>
                                        Country
                                    </label>
                                    <select id="country" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); cursor: pointer;" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'">
                                        <option value="">Select Country</option>
                                        <option value="Philippines" {% if genius.country == 'Philippines' %}selected{% endif %}>Philippines</option>
                                        <option value="Nigeria" {% if genius.country == 'Nigeria' %}selected{% endif %}>Nigeria</option>
                                        <option value="United States" {% if genius.country == 'United States' %}selected{% endif %}>United States</option>
                                        <option value="United Kingdom" {% if genius.country == 'United Kingdom' %}selected{% endif %}>United Kingdom</option>
                                        <option value="Canada" {% if genius.country == 'Canada' %}selected{% endif %}>Canada</option>
                                        <option value="Australia" {% if genius.country == 'Australia' %}selected{% endif %}>Australia</option>
                                        <option value="Germany" {% if genius.country == 'Germany' %}selected{% endif %}>Germany</option>
                                        <option value="France" {% if genius.country == 'France' %}selected{% endif %}>France</option>
                                        <option value="Japan" {% if genius.country == 'Japan' %}selected{% endif %}>Japan</option>
                                        <option value="China" {% if genius.country == 'China' %}selected{% endif %}>China</option>
                                        <option value="India" {% if genius.country == 'India' %}selected{% endif %}>India</option>
                                        <option value="Brazil" {% if genius.country == 'Brazil' %}selected{% endif %}>Brazil</option>
                                        <option value="South Africa" {% if genius.country == 'South Africa' %}selected{% endif %}>South Africa</option>
                                        <!-- Add a fallback option for any other country value -->
                                        {% if genius.country and genius.country not in ['Philippines', 'Nigeria', 'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Japan', 'China', 'India', 'Brazil', 'South Africa'] %}
                                        <option value="{{ genius.country }}" selected>{{ genius.country }}</option>
                                        {% endif %}
                                    </select>
                                </div>

                                <!-- Language Field -->
                                <div style="position: relative;">
                                    <label for="language" style="display: block; font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem; color: #667eea; text-transform: uppercase; letter-spacing: 0.5px; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-language" style="font-size: 0.8rem;"></i>
                                        Language
                                    </label>
                                    <select id="language" style="width: 100%; padding: 1rem 1.25rem; border: 2px solid rgba(102, 126, 234, 0.1); border-radius: 12px; font-size: 1rem; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(10px); color: #333; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); cursor: pointer;" onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'" onblur="this.style.borderColor='rgba(102, 126, 234, 0.1)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'; this.style.transform='translateY(0)'">
                                        <option value="English" selected>English</option>
                                        <option value="French">French</option>
                                        <option value="Spanish">Spanish</option>
                                        <option value="German">German</option>
                                        <option value="Chinese">Chinese</option>
                                        <option value="Japanese">Japanese</option>
                                        <option value="Arabic">Arabic</option>
                                        <option value="Russian">Russian</option>
                                        <option value="Portuguese">Portuguese</option>
                                        <option value="Hindi">Hindi</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Enhanced Action Buttons -->
                            <div style="display: flex; justify-content: flex-end; align-items: center; gap: 1rem; margin-top: 2.5rem;">
                                <button id="profileBackBtn" style="padding: 0.75rem 1.5rem; background: white; color: #667eea; border: 2px solid #667eea; border-radius: 25px; font-size: 0.95rem; font-weight: 600; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 0.5rem;" onmouseover="this.style.background='#f8fafc'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='white'; this.style.transform='translateY(0)'">
                                    <i class="fas fa-arrow-left" style="font-size: 0.8rem;"></i>
                                    Back
                                </button>
                                <button id="saveProfileBtn" style="padding: 0.75rem 2rem; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 25px; font-size: 0.95rem; font-weight: 600; cursor: pointer; transition: all 0.2s; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3); display: flex; align-items: center; gap: 0.5rem;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(102, 126, 234, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(102, 126, 234, 0.3)'">
                                    Save
                                    <i class="fas fa-check" style="font-size: 0.8rem;"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Portfolio View/Publish -->
    <div class="modal" id="portfolioViewModal">
        <div class="modal-content" style="max-width: 1400px; width: 95%; max-height: 90vh; overflow: hidden;">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content" style="display: flex; justify-content: space-between; align-items: center; padding: 1rem 2rem;">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <button class="copy-link-btn" style="background: #f0f0f0; border: 1px solid #ddd; padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-link"></i> Copy link
                        </button>
                        <button class="modal-close-btn" id="portfolioViewCloseBtn" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666;">×</button>
                    </div>
                </div>
            </header>

            <!-- Portfolio Content -->
            <div class="modal-body" style="padding: 2rem; height: calc(90vh - 120px); overflow: hidden;">
                <div class="portfolio-view-container" style="height: 100%;">
                    <!-- Project Header with Title and Images Side by Side -->
                    <div class="project-header-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; height: 100%; align-items: start;">
                        <!-- Left Side - Project Info and Details (Fixed, No Scroll) -->
                        <div class="project-info" style="overflow: hidden;">
                            <h1 class="project-title" style="font-size: 2rem; font-weight: 700; color: #333; margin-bottom: 0.5rem;">Sample Project Title</h1>
                            <div class="project-meta" style="display: flex; align-items: center; gap: 1rem; margin-bottom: 2rem;">
                                <span class="project-role" style="color: #666; font-size: 1rem;">My role: <strong>Full Stack Developer</strong></span>
                                <span class="project-date" style="color: #666; font-size: 0.9rem;">Published on Feb 24, 2025</span>
                            </div>

                            <!-- Project Details moved here -->
                            <div class="project-section" style="margin-bottom: 2rem;">
                                <h3 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: #333;">Project description</h3>
                                <p class="project-description" style="color: #666; line-height: 1.6; font-size: 0.95rem;">
                                    This is a comprehensive web development project that involved creating a modern, responsive website with advanced functionality. The project included both frontend and backend development, database design, and deployment.
                                </p>
                            </div>

                            <div class="project-section" style="margin-bottom: 2rem;">
                                <h3 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: #333;">Skills and Deliverables</h3>
                                <div class="skills-tags" style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                    <span class="skill-tag" style="background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;">Web Development</span>
                                    <span class="skill-tag" style="background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;">React.js</span>
                                    <span class="skill-tag" style="background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;">Node.js</span>
                                    <span class="skill-tag" style="background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;">Database Design</span>
                                </div>
                            </div>

                            <div class="project-section related-job-section" style="margin-bottom: 2rem; display: none;">
                                <h3 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: #333;">Related GigGenius Job</h3>
                                <p class="related-job-text" style="color: #666; line-height: 1.6; font-size: 0.95rem;">
                                    <!-- Related job will be populated here -->
                                </p>
                            </div>

                            <div class="project-actions" style="margin-top: 2rem;">
                                <button class="report-btn" style="background: none; border: none; color: #666; font-size: 0.9rem; cursor: pointer; text-decoration: underline;">
                                    Report an issue
                                </button>
                            </div>
                        </div>

                        <!-- Right Side - Project Images (Scrollable Only) -->
                        <div class="project-content-container" style="margin: 0; height: 100%; overflow-y: auto; overflow-x: hidden; padding-right: 1rem;">
                            <div class="project-section content-section" style="margin-bottom: 0; display: none;">
                                <div class="project-content" style="color: #666; line-height: 1.6; font-size: 0.95rem;">
                                    <!-- Project content will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Certification Edit -->
    <div class="modal" id="certificationEditModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Certification *</h2>
                <textarea class="modal-textarea" placeholder="Edit your certification details..."></textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="certificationBackBtn">Back</button>
                    <button class="modal-next-btn">Save Changes</button>
                </div>
            </div>
        </div>
    </div>



    <!-- Modal for Portfolio Reorder -->
    <div class="modal" id="portfolioReorderModal">
        <div class="modal-content" style="max-width: 600px;">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content" style="justify-content: space-between;">
                    <h2 style="margin: 0; font-size: 1.5rem; font-weight: 600;">Reorder portfolio projects</h2>
                    <button class="modal-close-btn" id="portfolioReorderCloseBtn" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666;">×</button>
                </div>
            </header>

            <!-- Reorder Section -->
            <div class="modal-body" style="padding: 2rem;">
                <div class="reorder-list">
                    <!-- Portfolio Item 1 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Bryan's Handyman & Construction LLC</h4>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 2 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Virtual Assistant Services</h4>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 3 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Graphics Design and Web Development</h4>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 4 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">GigGenius Web and App Development</h4>
                            <div style="background: #28a745; color: white; padding: 0.2rem 0.5rem; border-radius: 4px; font-size: 0.75rem; display: inline-block; margin-top: 0.25rem;">Reorder portfolio projects</div>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 5 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Bryan's Handyman and Construction LLC</h4>
                            <div style="color: #666; font-size: 0.875rem;">April 2024</div>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 6 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">SMB Paralegal Services</h4>
                            <div style="color: #666; font-size: 0.875rem;">April 2024</div>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="modal-buttons" style="display: flex; justify-content: flex-end; gap: 1rem; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #eee;">
                    <button class="modal-back-btn" id="portfolioReorderCancelBtn" style="background: none; border: 1px solid #ddd; color: #666; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer;">Cancel</button>
                    <button class="modal-next-btn" id="portfolioReorderSaveBtn" style="background: #28a745; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-weight: 600;">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Test modal function - for debugging
        function testModal() {
            console.log('Testing notification modal...');
            showPortfolioNotification('Test modal is working!', 'success', 'fas fa-check');
        }

        // Modal notification system
        function showPortfolioNotification(message, type = 'success', icon = null) {
            const modal = document.getElementById('notificationModal');
            const iconElement = document.getElementById('notificationIcon');
            const titleElement = document.getElementById('notificationTitle');
            const messageElement = document.getElementById('notificationMessage');

            // Set icon and title based on type
            let iconClass = 'fas fa-check-circle';
            let title = 'Success';

            switch(type) {
                case 'success':
                    iconClass = icon || 'fas fa-check-circle';
                    title = 'Success';
                    iconElement.className = 'notification-modal-icon success';
                    break;
                case 'error':
                    iconClass = icon || 'fas fa-exclamation-circle';
                    title = 'Error';
                    iconElement.className = 'notification-modal-icon error';
                    break;
                case 'warning':
                    iconClass = icon || 'fas fa-exclamation-triangle';
                    title = 'Warning';
                    iconElement.className = 'notification-modal-icon warning';
                    break;
                case 'info':
                    iconClass = icon || 'fas fa-info-circle';
                    title = 'Information';
                    iconElement.className = 'notification-modal-icon info';
                    break;
            }

            // Update modal content
            iconElement.innerHTML = `<i class="${iconClass}"></i>`;
            titleElement.textContent = title;
            messageElement.textContent = message;

            // Show modal
            if (modal) {
                modal.style.display = 'flex';
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
                console.log('Notification modal shown:', message);

                // Auto-close after 3 seconds for success messages
                if (type === 'success') {
                    setTimeout(() => {
                        closeNotificationModal();
                    }, 3000);
                }
            } else {
                console.error('Notification modal not found');
            }
        }

        function closeNotificationModal() {
            const modal = document.getElementById('notificationModal');
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('active');
                document.body.style.overflow = 'auto';
                console.log('Notification modal closed');
            }
        }

        // Close notification modal when clicking outside
        document.getElementById('notificationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeNotificationModal();
            }
        });


    </script>
    <script>
        // Show More functionality for summary text
        const summaryText = document.getElementById('summaryText');
        const showMoreBtn = document.getElementById('showMore');
        const editSummaryBtn = document.getElementById('editSummaryBtn');

        showMoreBtn.addEventListener('click', () => {
            summaryText.textContent = "Collapsible text is perfect for longer content like paragraphs and descriptions. It's a great way to give people more information while keeping your layout clean.";
            showMoreBtn.style.display = 'none';
            // Edit button is already visible
        });

        // Video functionality
        const videoContainer = document.getElementById('videoContainer');
        const videoPlaceholder = document.getElementById('videoPlaceholder');
        const videoPreview = document.getElementById('videoPreview');
        const videoPlayer = document.getElementById('videoPlayer');
        const uploadVideoBtn = document.getElementById('uploadVideoBtn');
        const videoUpload = document.getElementById('videoUpload');
        const previewVideo = document.getElementById('previewVideo');
        const saveVideoBtn = document.getElementById('saveVideoBtn');
        const cancelVideoBtn = document.getElementById('cancelVideoBtn');
        const deleteVideoBtn = document.getElementById('deleteVideoBtn');
        const replaceVideoBtn = document.getElementById('replaceVideoBtn');
        const expandBtn = document.getElementById('expandBtn');
        const video = document.getElementById('profileVideo');

        // Check if there's an existing video from the database
        let hasVideo = false;
        let selectedVideoFile = null;

        // Show appropriate view based on video existence
        function updateVideoView() {
            if (hasVideo) {
                videoPlaceholder.style.display = 'none';
                videoPreview.style.display = 'none';
                videoPlayer.style.display = 'block';
            } else if (selectedVideoFile) {
                videoPlaceholder.style.display = 'none';
                videoPreview.style.display = 'block';
                videoPlayer.style.display = 'none';
            } else {
                videoPlaceholder.style.display = 'flex';
                videoPreview.style.display = 'none';
                videoPlayer.style.display = 'none';
            }
        }

        // Check for existing video when page loads
        async function checkExistingVideo() {
            try {
                // Use the verification endpoint first
                const checkResponse = await fetch('/check_profile_video_exists');
                const checkData = await checkResponse.json();

                if (checkData.exists) {
                    // Video exists, set up the video player
                    hasVideo = true;
                    video.src = `/api/profile-video/genius/{{ genius.id }}?t=${new Date().getTime()}`;
                    console.log('✅ Existing profile video found:', checkData.filename);
                } else {
                    // No video found
                    hasVideo = false;
                    console.log('ℹ️ No existing profile video found');
                }
            } catch (error) {
                console.error('❌ Error checking existing video:', error);
                // Fallback to the old method
                try {
                    const response = await fetch(`/api/profile-video/genius/{{ genius.id }}`);
                    if (response.ok) {
                        hasVideo = true;
                        video.src = `/api/profile-video/genius/{{ genius.id }}?t=${new Date().getTime()}`;
                        console.log('✅ Existing profile video found (fallback)');
                    } else {
                        hasVideo = false;
                        console.log('ℹ️ No existing profile video found (fallback)');
                    }
                } catch (fallbackError) {
                    console.error('❌ Fallback check also failed:', fallbackError);
                    hasVideo = false;
                }
            }
            updateVideoView();
        }

        // Initialize video view
        checkExistingVideo();

        // Upload video functionality
        if (uploadVideoBtn && videoUpload) {
            uploadVideoBtn.addEventListener('click', () => {
                videoUpload.click();
            });

            videoUpload.addEventListener('change', (event) => {
                const file = event.target.files[0];
                if (file && file.type.startsWith('video/')) {
                    // Validate file size (50MB max)
                    const maxSize = 50 * 1024 * 1024; // 50MB
                    if (file.size > maxSize) {
                        showPortfolioNotification('Video file size must be less than 50MB. Current size: ' + (file.size / (1024 * 1024)).toFixed(2) + 'MB', 'error', 'fas fa-exclamation-triangle');
                        return;
                    }

                    // Validate video format
                    const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm', 'video/mkv'];
                    if (!allowedTypes.includes(file.type)) {
                        showPortfolioNotification('Please select a supported video format: MP4, AVI, MOV, WMV, WebM, or MKV', 'error', 'fas fa-file-video');
                        return;
                    }

                    selectedVideoFile = file;

                    // Show preview
                    const videoURL = URL.createObjectURL(file);
                    previewVideo.src = videoURL;
                    updateVideoView();

                    // Show file info
                    const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
                    showPortfolioNotification(`Video selected: ${file.name} (${fileSizeMB}MB)`, 'success', 'fas fa-video');
                    console.log('Video selected for preview:', file.name, 'Size:', fileSizeMB + 'MB');
                } else {
                    showPortfolioNotification('Please select a valid video file.', 'error', 'fas fa-exclamation-circle');
                }
            });

            // Add drag and drop functionality to the video placeholder
            const videoPlaceholder = document.getElementById('videoPlaceholder');

            if (videoPlaceholder) {
                // Prevent default drag behaviors
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    videoPlaceholder.addEventListener(eventName, preventDefaults, false);
                });

                // Highlight drop area when item is dragged over it
                ['dragenter', 'dragover'].forEach(eventName => {
                    videoPlaceholder.addEventListener(eventName, highlight, false);
                });

                ['dragleave', 'drop'].forEach(eventName => {
                    videoPlaceholder.addEventListener(eventName, unhighlight, false);
                });

                // Handle dropped files
                videoPlaceholder.addEventListener('drop', handleDrop, false);

                function preventDefaults(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }

                function highlight(e) {
                    videoPlaceholder.style.borderColor = '#007bff';
                    videoPlaceholder.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                }

                function unhighlight(e) {
                    videoPlaceholder.style.borderColor = '#dee2e6';
                    videoPlaceholder.style.backgroundColor = '#f8f9fa';
                }

                function handleDrop(e) {
                    const dt = e.dataTransfer;
                    const files = dt.files;

                    if (files.length > 0) {
                        const file = files[0];
                        if (file && file.type.startsWith('video/')) {
                            // Check file size (50MB max)
                            const maxSize = 50 * 1024 * 1024; // 50MB
                            if (file.size > maxSize) {
                                showPortfolioNotification('Video file size must be less than 50MB. Current size: ' + (file.size / (1024 * 1024)).toFixed(2) + 'MB', 'error', 'fas fa-exclamation-triangle');
                                return;
                            }

                            // Validate video format
                            const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm', 'video/mkv'];
                            if (!allowedTypes.includes(file.type)) {
                                showPortfolioNotification('Please select a supported video format: MP4, AVI, MOV, WMV, WebM, or MKV', 'error', 'fas fa-file-video');
                                return;
                            }

                            selectedVideoFile = file;

                            // Show preview
                            const videoURL = URL.createObjectURL(file);
                            previewVideo.src = videoURL;
                            updateVideoView();

                            // Show file info
                            const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
                            showPortfolioNotification(`Video dropped and selected: ${file.name} (${fileSizeMB}MB)`, 'success', 'fas fa-video');
                            console.log('Video dropped and selected:', file.name, 'Size:', fileSizeMB + 'MB');
                        } else {
                            showPortfolioNotification('Please drop a valid video file.', 'error', 'fas fa-exclamation-circle');
                        }
                    }
                }
            }
        }

        // Save video functionality
        if (saveVideoBtn) {
            saveVideoBtn.addEventListener('click', async () => {
                if (!selectedVideoFile) {
                    showPortfolioNotification('No video selected to save', 'error', 'fas fa-exclamation-circle');
                    return;
                }

                // Double-check file size before upload
                const maxSize = 50 * 1024 * 1024; // 50MB
                if (selectedVideoFile.size > maxSize) {
                    showPortfolioNotification('Video file is too large. Please select a file smaller than 50MB.', 'error', 'fas fa-exclamation-triangle');
                    return;
                }

                // Show loading state
                saveVideoBtn.disabled = true;
                saveVideoBtn.classList.add('loading');
                saveVideoBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

                try {
                    const formData = new FormData();
                    formData.append('video', selectedVideoFile);

                    const response = await fetch('/upload_profile_video', {
                        method: 'POST',
                        body: formData
                    });

                    const data = await response.json();
                    if (data.success) {
                        console.log('✅ Video uploaded successfully');
                        showPortfolioNotification('Profile video saved successfully!', 'success', 'fas fa-video');

                        // Move from preview to saved state
                        hasVideo = true;
                        video.src = `/api/profile-video/genius/{{ genius.id }}?t=${new Date().getTime()}`;
                        selectedVideoFile = null;
                        updateVideoView();
                    } else {
                        console.error('❌ Video upload failed:', data.error);
                        showPortfolioNotification('Error saving video: ' + data.error, 'error', 'fas fa-exclamation-triangle');
                    }
                } catch (error) {
                    console.error('❌ Video upload error:', error);
                    showPortfolioNotification('An error occurred while saving the video: ' + error.message, 'error', 'fas fa-exclamation-circle');
                } finally {
                    // Reset button state
                    saveVideoBtn.disabled = false;
                    saveVideoBtn.classList.remove('loading');
                    saveVideoBtn.innerHTML = '<i class="fas fa-save"></i> Save Video';
                }
            });
        }

        // Cancel video functionality
        if (cancelVideoBtn) {
            cancelVideoBtn.addEventListener('click', () => {
                selectedVideoFile = null;
                previewVideo.src = '';
                updateVideoView();
                console.log('Video selection cancelled');
            });
        }

        // Replace video functionality
        if (replaceVideoBtn) {
            replaceVideoBtn.addEventListener('click', () => {
                videoUpload.click();
            });
        }

        // Delete video functionality
        if (deleteVideoBtn) {
            deleteVideoBtn.addEventListener('click', () => {
                showDeleteModal();
            });
        }

        // Delete modal functions
        function showDeleteModal() {
            const modal = document.getElementById('deleteModal');
            if (modal) {
                modal.style.display = 'flex';
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
                console.log('Delete modal shown');
            } else {
                console.error('Delete modal not found');
            }
        }

        function closeDeleteModal() {
            const modal = document.getElementById('deleteModal');
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('active');
                document.body.style.overflow = 'auto';
                console.log('Delete modal closed');
            }
        }

        async function confirmDeleteVideo() {
            try {
                // Show loading state
                const confirmBtn = document.querySelector('.delete-modal-btn.confirm');
                const originalText = confirmBtn.textContent;
                confirmBtn.textContent = 'Deleting...';
                confirmBtn.disabled = true;

                // First, clear the video source to release file handles
                if (video) {
                    video.pause();
                    video.src = '';
                    video.load(); // Force the video element to release the file
                    video.removeAttribute('src'); // Additional cleanup
                }

                // Also clear any modal video if it exists
                const modalVideo = document.getElementById('modalVideo');
                if (modalVideo) {
                    modalVideo.pause();
                    modalVideo.src = '';
                    modalVideo.load();
                    modalVideo.removeAttribute('src');
                }

                // Clear any other video elements that might be holding the file
                const allVideos = document.querySelectorAll('video');
                allVideos.forEach(v => {
                    if (v.src && v.src.includes('profile-video')) {
                        v.pause();
                        v.src = '';
                        v.load();
                        v.removeAttribute('src');
                    }
                });

                // Wait longer for the browser to release file handles
                await new Promise(resolve => setTimeout(resolve, 500));

                const response = await fetch('/delete_profile_video', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                // Reset button state
                confirmBtn.textContent = originalText;
                confirmBtn.disabled = false;

                if (data.success) {
                    // Double-check that the video is actually gone
                    try {
                        const checkResponse = await fetch('/check_profile_video_exists');
                        const checkData = await checkResponse.json();

                        if (!checkData.exists) {
                            // Video is confirmed deleted
                            hasVideo = false;
                            updateVideoView();
                            console.log('✅ Video deleted and verified');
                            showPortfolioNotification('Profile video deleted successfully!', 'success', 'fas fa-trash-alt');
                        } else {
                            // Video still exists, but deletion was reported as successful
                            console.log('⚠️ Video deletion reported success but file still exists');
                            hasVideo = false; // Still update UI since backend says it's deleted
                            updateVideoView();
                            showPortfolioNotification('Profile video deleted successfully!', 'success', 'fas fa-trash-alt');
                        }
                    } catch (checkError) {
                        console.error('❌ Error verifying deletion:', checkError);
                        // Assume deletion was successful since backend reported success
                        hasVideo = false;
                        updateVideoView();
                        showPortfolioNotification('Profile video deleted successfully!', 'success', 'fas fa-trash-alt');
                    }
                    closeDeleteModal();
                } else {
                    console.error('❌ Video deletion failed:', data.error);
                    showPortfolioNotification('Error deleting video: ' + data.error, 'error', 'fas fa-exclamation-triangle');
                    closeDeleteModal();
                }
            } catch (error) {
                // Reset button state on error
                const confirmBtn = document.querySelector('.delete-modal-btn.confirm');
                confirmBtn.textContent = 'Delete';
                confirmBtn.disabled = false;

                console.error('❌ Video deletion error:', error);
                showPortfolioNotification('An error occurred while deleting the video: ' + error.message, 'error', 'fas fa-exclamation-circle');
                closeDeleteModal();
            }
        }

        // Close delete modal when clicking outside
        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });

        // Video expand/collapse functionality
        if (expandBtn) {
            expandBtn.addEventListener('click', () => {
                videoContainer.classList.toggle('expanded');
                if (videoContainer.classList.contains('expanded')) {
                    expandBtn.innerHTML = '<i class="fas fa-compress"></i> Exit Fullscreen';
                    document.body.style.overflow = 'hidden';
                } else {
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                    document.body.style.overflow = '';
                }
            });
        }

        // Close expanded video when clicking outside
        document.addEventListener('click', (event) => {
            if (videoContainer && videoContainer.classList.contains('expanded') &&
                !videoPlayer.contains(event.target) &&
                !expandBtn.contains(event.target)) {
                videoContainer.classList.remove('expanded');
                if (expandBtn) {
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                }
                document.body.style.overflow = '';
            }
        });

        // Close expanded video when pressing Escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && videoContainer && videoContainer.classList.contains('expanded')) {
                videoContainer.classList.remove('expanded');
                if (expandBtn) {
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                }
                document.body.style.overflow = '';
            }
        });

        // This section has been moved to the DOMContentLoaded event below to avoid conflicts

        // Mobile Menu Toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');

        if (mobileMenuBtn && mobileMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('active');
                if (mobileMenu.classList.contains('active')) {
                    mobileMenuBtn.innerHTML = '<i class="fas fa-times"></i>';
                } else {
                    mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                }
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (event) => {
                if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
                    mobileMenu.classList.remove('active');
                    mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - Initializing modals...');

            // Test modal functionality
            console.log('Testing modal elements...');
            const testModals = [
                'professionalSummaryModal',
                'introductionModal',
                'editProfileModal',
                'portfolioViewModal',
                'portfolioReorderModal',
                'certificationEditModal',
                'deleteModal',
                'notificationModal',
                'addPortfolioModal'
            ];

            testModals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                console.log(`${modalId}:`, modal ? 'Found' : 'NOT FOUND');
            });

            // Debug: Log the genius data
            console.log('Genius country value:', '{{ genius.country }}');
            console.log('Genius data:', {
                country: '{{ genius.country }}',
                availability: '{{ genius.availability }}',
                expertise: '{{ genius.expertise }}',
                position: '{{ genius.position }}'
            });

            // Modal functionality
            const professionalSummaryModal = document.getElementById('professionalSummaryModal');
            const introductionModal = document.getElementById('introductionModal');
            const editProfileModal = document.getElementById('editProfileModal');
            const portfolioViewModal = document.getElementById('portfolioViewModal');

            const portfolioReorderModal = document.getElementById('portfolioReorderModal');
            const certificationEditModal = document.getElementById('certificationEditModal');

            // Edit buttons
            const editSummary = document.getElementById('editSummaryBtn');
            const editIntroduction = document.getElementById('editIntroductionBtn');
            const editProfile = document.getElementById('editProfileBtn');
            const portfolioAdd = document.getElementById('portfolioAddBtn');
            const portfolioRefresh = document.querySelector('.portfolio-refresh-btn');
            const portfolioViewCloseBtn = document.getElementById('portfolioViewCloseBtn');
            const portfolioAddCloseBtn = document.getElementById('portfolioAddCloseBtn');
            const portfolioReorderCloseBtn = document.getElementById('portfolioReorderCloseBtn');
            const certificationEdit1 = document.getElementById('certificationEditBtn1');
            const certificationEdit2 = document.getElementById('certificationEditBtn2');

            // Back buttons
            const summaryBackBtn = document.getElementById('summaryBackBtn');
            const introBackBtn = document.getElementById('introBackBtn');
            const profileBackBtn = document.getElementById('profileBackBtn');
            const portfolioAddBackBtn = document.getElementById('portfolioAddBackBtn');
            const portfolioReorderCancelBtn = document.getElementById('portfolioReorderCancelBtn');
            const certificationBackBtn = document.getElementById('certificationBackBtn');

            console.log('Modal elements found:', {
                professionalSummaryModal: !!professionalSummaryModal,
                editSummary: !!editSummary,
                editIntroduction: !!editIntroduction,
                editProfile: !!editProfile
            });

            // Close modals function
            function closeModal(modal) {
                if (modal) {
                    modal.style.display = 'none';
                    modal.classList.remove('active');
                    document.body.style.overflow = 'auto';
                    console.log('Modal closed:', modal.id);
                }
            }

            // Open modals - with null checks and debugging
            if (editSummary && professionalSummaryModal) {
                editSummary.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Edit Summary clicked');

                    // Load current text into textarea when opening modal
                    const summaryText = document.getElementById('summaryText');
                    const professionalSummaryTextarea = document.getElementById('professionalSummaryTextarea');

                    if (summaryText && professionalSummaryTextarea) {
                        // Get the current text, excluding the placeholder text
                        const currentText = summaryText.textContent || summaryText.innerText || '';
                        const placeholderText = 'Add a professional summary to showcase your skills and experience to potential clients. Click the Edit button to get started.';

                        if (currentText.trim() === placeholderText.trim()) {
                            professionalSummaryTextarea.value = '';
                        } else {
                            professionalSummaryTextarea.value = currentText.trim();
                        }
                    }

                    professionalSummaryModal.style.display = 'flex';
                    professionalSummaryModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (editIntroduction && introductionModal) {
                editIntroduction.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Edit Introduction clicked');
                    introductionModal.style.display = 'flex';
                    introductionModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (editProfile && editProfileModal) {
                editProfile.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Edit Profile clicked');

                    try {
                        // Reset profile photo to show current photo when modal opens
                        const currentProfilePhoto = document.getElementById('currentProfilePhoto');
                        const navProfilePhoto = document.getElementById('navProfilePhoto');
                        if (currentProfilePhoto) {
                            // Force reload the current photo with timestamp to avoid cache
                            const timestamp = new Date().getTime();
                            currentProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                            // Also update navigation photo to ensure consistency
                            if (navProfilePhoto) {
                                navProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                            }
                        }

                        // Fetch current profile data from database
                        const response = await fetch('/api/genius/profile_data');
                        const data = await response.json();

                        if (data.success && data.profile_data) {
                            const profileData = data.profile_data;

                            // Set form values with database data
                            const emailField = document.getElementById('email');
                            const mobileField = document.getElementById('mobile');
                            const positionField = document.getElementById('position');
                            const expertiseField = document.getElementById('expertise');
                            const rateField = document.getElementById('rate');
                            const availabilityField = document.getElementById('availability');
                            const countryField = document.getElementById('country');
                            const languageField = document.getElementById('language');

                            if (emailField) emailField.value = profileData.email || '';
                            if (mobileField) mobileField.value = profileData.mobile || '';
                            if (positionField) positionField.value = profileData.position || '';
                            if (expertiseField) expertiseField.value = profileData.expertise || 'Beginner';
                            if (rateField) rateField.value = profileData.hourly_rate || '';
                            if (availabilityField) availabilityField.value = profileData.availability || 'fulltime';
                            if (countryField) countryField.value = profileData.country || '';
                            if (languageField) languageField.value = profileData.language || 'English';
                        } else {
                            console.error('Failed to fetch profile data:', data.error);
                        }
                    } catch (error) {
                        console.error('Error fetching profile data:', error);
                    }

                    editProfileModal.style.display = 'flex';
                    editProfileModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }



            if (portfolioRefresh && portfolioReorderModal) {
                portfolioRefresh.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Portfolio Refresh clicked');
                    portfolioReorderModal.style.display = 'flex';
                    portfolioReorderModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (certificationEdit1 && certificationEditModal) {
                certificationEdit1.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Certification Edit 1 clicked');
                    certificationEditModal.style.display = 'flex';
                    certificationEditModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (certificationEdit2 && certificationEditModal) {
                certificationEdit2.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Certification Edit 2 clicked');
                    certificationEditModal.style.display = 'flex';
                    certificationEditModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            // Close modals with back buttons
            if (summaryBackBtn) {
                summaryBackBtn.addEventListener('click', () => {
                    console.log('Summary back button clicked');
                    closeModal(professionalSummaryModal);
                });
            }

            if (introBackBtn) {
                introBackBtn.addEventListener('click', () => {
                    console.log('Intro back button clicked');
                    closeModal(introductionModal);
                });
            }

            if (profileBackBtn) {
                profileBackBtn.addEventListener('click', () => {
                    console.log('Profile back button clicked');
                    closeModal(editProfileModal);
                });
            }

            if (portfolioViewCloseBtn) {
                portfolioViewCloseBtn.addEventListener('click', () => {
                    console.log('Portfolio view close button clicked');
                    closeModal(portfolioViewModal);
                });
            }



            if (portfolioReorderCloseBtn) {
                portfolioReorderCloseBtn.addEventListener('click', () => {
                    console.log('Portfolio reorder close button clicked');
                    closeModal(portfolioReorderModal);
                });
            }

            if (portfolioReorderCancelBtn) {
                portfolioReorderCancelBtn.addEventListener('click', () => {
                    console.log('Portfolio reorder cancel button clicked');
                    closeModal(portfolioReorderModal);
                });
            }

            if (certificationBackBtn) {
                certificationBackBtn.addEventListener('click', () => {
                    console.log('Certification back button clicked');
                    closeModal(certificationEditModal);
                });
            }

            // Close modals when clicking outside
            window.addEventListener('click', (event) => {
                if (event.target === professionalSummaryModal) {
                    closeModal(professionalSummaryModal);
                }
                if (event.target === introductionModal) {
                    closeModal(introductionModal);
                }
                if (event.target === editProfileModal) {
                    closeModal(editProfileModal);
                }
                if (event.target === portfolioViewModal) {
                    closeModal(portfolioViewModal);
                }

                if (event.target === portfolioReorderModal) {
                    closeModal(portfolioReorderModal);
                }
                if (event.target === certificationEditModal) {
                    closeModal(certificationEditModal);
                }
            });

            // Close modals with Escape key
            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape') {
                    closeModal(professionalSummaryModal);
                    closeModal(introductionModal);
                    closeModal(editProfileModal);
                    closeModal(portfolioViewModal);

                    closeModal(portfolioReorderModal);
                    closeModal(certificationEditModal);
                }
            });

            // For mobile devices, make dropdown items clickable to expand
            const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');

            dropdownTriggers.forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    if (window.innerWidth < 768) {
                        e.preventDefault();
                        const dropdown = this.nextElementSibling;
                        dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    const dropdownMenus = document.querySelectorAll('.dropdown-menu');
                    dropdownMenus.forEach(menu => {
                        if (window.innerWidth < 768) {
                            menu.style.display = 'none';
                        }
                    });
                }

                // Close profile dropdown when clicking outside
                if (!e.target.closest('.profile-dropdown')) {
                    const profileDropdown = document.getElementById('profileDropdown');
                    if (profileDropdown) {
                        profileDropdown.classList.remove('active');
                    }
                }

                // Close notification dropdown when clicking outside
                if (!e.target.closest('.notification-dropdown')) {
                    const notificationDropdown = document.getElementById('notificationDropdown');
                    if (notificationDropdown) {
                        notificationDropdown.classList.remove('active');
                    }
                }
            });

            // Profile dropdown toggle
            const profileIcon = document.getElementById('profileIcon');
            const profileDropdown = document.getElementById('profileDropdown');

            if (profileIcon && profileDropdown) {
                profileIcon.addEventListener('click', function(e) {
                    e.stopPropagation();
                    profileDropdown.classList.toggle('active');

                    // Close notification dropdown if open
                    const notificationDropdown = document.getElementById('notificationDropdown');
                    if (notificationDropdown && notificationDropdown.classList.contains('active')) {
                        notificationDropdown.classList.remove('active');
                    }
                });

                profileDropdown.addEventListener('click', (event) => {
                    event.stopPropagation();
                });
            }

            // Notification dropdown toggle
            const notificationBtn = document.getElementById('notificationBtn');
            const notificationDropdown = document.getElementById('notificationDropdown');
            const markAllReadBtn = document.getElementById('markAllRead');

            if (notificationBtn && notificationDropdown) {
                notificationBtn.addEventListener('click', function(event) {
                    event.stopPropagation();
                    notificationDropdown.classList.toggle('active');

                    // Close profile dropdown if open
                    if (profileDropdown && profileDropdown.classList.contains('active')) {
                        profileDropdown.classList.remove('active');
                    }
                });

                notificationDropdown.addEventListener('click', (event) => {
                    event.stopPropagation();
                });

                // Mark all notifications as read
                if (markAllReadBtn) {
                    markAllReadBtn.addEventListener('click', function() {
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                        unreadNotifications.forEach(notification => {
                            notification.classList.remove('unread');
                        });

                        // Remove the notification indicator
                        const indicator = document.querySelector('.notification-indicator');
                        if (indicator) {
                            indicator.style.display = 'none';
                        }
                    });
                }

                // Mark individual notification as read when clicked
                const notificationItems = document.querySelectorAll('.notification-item');
                notificationItems.forEach(item => {
                    item.addEventListener('click', function() {
                        this.classList.remove('unread');

                        // Check if there are any unread notifications left
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                        if (unreadNotifications.length === 0) {
                            const indicator = document.querySelector('.notification-indicator');
                            if (indicator) {
                                indicator.style.display = 'none';
                            }
                        }
                    });
                });
            }

            // Professional Summary Next Button functionality
            const summaryNextBtn = document.getElementById('summaryNextBtn');
            const professionalSummaryTextarea = document.getElementById('professionalSummaryTextarea');

            if (summaryNextBtn && professionalSummaryTextarea) {
                summaryNextBtn.addEventListener('click', async function(e) {
                    e.preventDefault();

                    const professionalSummary = professionalSummaryTextarea.value.trim();

                    if (!professionalSummary) {
                        showPortfolioNotification('Please enter a professional summary before saving.', 'error', 'fas fa-exclamation-circle');
                        return;
                    }

                    // Show loading state
                    summaryNextBtn.disabled = true;
                    summaryNextBtn.textContent = 'Saving...';

                    try {
                        const response = await fetch('/update_professional_summary', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                professional_summary: professionalSummary
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            // Update the display text
                            const summaryText = document.getElementById('summaryText');
                            if (summaryText) {
                                if (professionalSummary.trim()) {
                                    summaryText.innerHTML = professionalSummary;
                                } else {
                                    summaryText.innerHTML = '<span style="color: #6b7280; font-style: italic;">Add a professional summary to showcase your skills and experience to potential clients. Click the Edit button to get started.</span>';
                                }
                            }

                            // Close the modal
                            closeModal(professionalSummaryModal);

                            // Show success notification
                            showPortfolioNotification('Professional summary has been updated successfully!', 'success', 'fas fa-user-edit');
                        } else {
                            showPortfolioNotification('Error: ' + (data.error || 'Failed to update professional summary'), 'error', 'fas fa-exclamation-triangle');
                        }
                    } catch (error) {
                        console.error('Error updating professional summary:', error);
                        showPortfolioNotification('An error occurred while updating your professional summary. Please try again.', 'error', 'fas fa-exclamation-circle');
                    } finally {
                        // Reset button state
                        summaryNextBtn.disabled = false;
                        summaryNextBtn.textContent = 'Next';
                    }
                });
            }

            // Introduction Next Button functionality
            const introNextBtn = document.getElementById('introNextBtn');
            const introductionTextarea = document.getElementById('introductionTextarea');

            if (introNextBtn && introductionTextarea) {
                introNextBtn.addEventListener('click', async function(e) {
                    e.preventDefault();

                    const introduction = introductionTextarea.value.trim();
                    console.log('Introduction content:', introduction);

                    if (!introduction) {
                        showPortfolioNotification('Please enter an introduction before saving.', 'error', 'fas fa-exclamation-circle');
                        return;
                    }

                    // Show loading state
                    introNextBtn.disabled = true;
                    introNextBtn.textContent = 'Saving...';

                    try {
                        console.log('Sending request to /introduction');
                        const response = await fetch('/introduction', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                introduction: introduction
                            })
                        });

                        console.log('Response status:', response.status);
                        const data = await response.json();
                        console.log('Response data:', data);

                        if (data.success) {
                            // Update the display text
                            const introductionDisplay = document.getElementById('introductionDisplay');
                            if (introductionDisplay) {
                                introductionDisplay.value = introduction;
                            }

                            // Close the modal
                            closeModal(introductionModal);

                            // Show success notification
                            showPortfolioNotification('Introduction updated successfully!', 'success', 'fas fa-user-circle');
                        } else {
                            showPortfolioNotification('Error: ' + (data.error || 'Failed to update introduction'), 'error', 'fas fa-exclamation-triangle');
                        }
                    } catch (error) {
                        console.error('Error updating introduction:', error);
                        showPortfolioNotification('An error occurred while updating your introduction. Please try again.', 'error', 'fas fa-exclamation-circle');
                    } finally {
                        // Reset button state
                        introNextBtn.disabled = false;
                        introNextBtn.textContent = 'Next';
                    }
                });
            }
            // Profile photo upload functionality
            const profileUpload = document.getElementById('profile-upload');
            const profilePhoto = document.querySelector('.profile-photo img');
            let selectedFile = null;
            let originalPhotoSrc = null; // Store original photo URL

            if (profileUpload) {
                // Store the original photo source when page loads
                originalPhotoSrc = profilePhoto.src;

                profileUpload.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        // Validate file type
                        if (!file.type.startsWith('image/')) {
                            showPortfolioNotification('Please select an image file (JPEG, PNG, etc.)', 'error', 'fas fa-image');
                            e.target.value = '';
                            return;
                        }

                        // Validate file size (2MB limit)
                        if (file.size > 2 * 1024 * 1024) {
                            showPortfolioNotification('File size must be less than 2MB', 'error', 'fas fa-exclamation-triangle');
                            e.target.value = '';
                            return;
                        }

                        selectedFile = file;

                        // Preview the new image
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            profilePhoto.src = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    } else {
                        // If no file selected, revert to original photo
                        selectedFile = null;
                        profilePhoto.src = originalPhotoSrc;
                    }
                });
            }

            // Profile save functionality
            const saveProfileBtn = document.getElementById('saveProfileBtn');
            if (saveProfileBtn) {
                saveProfileBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Edit Profile clicked - saving profile data');

                    // Get form values
                    const email = document.getElementById('email').value.trim();
                    const mobile = document.getElementById('mobile').value.trim();
                    const position = document.getElementById('position').value.trim();
                    const expertise = document.getElementById('expertise').value;
                    const hourly_rate = document.getElementById('rate').value;
                    const availability = document.getElementById('availability').value;
                    const country = document.getElementById('country').value;
                    const language = document.getElementById('language').value;

                    // Validate required fields
                    if (!email || !mobile || !position || !expertise || !hourly_rate || !availability || !country) {
                        showPortfolioNotification('Please fill in all required fields', 'error', 'fas fa-exclamation-circle');
                        return;
                    }

                    // Validate email format
                    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    if (!emailPattern.test(email)) {
                        showPortfolioNotification('Please enter a valid email address', 'error', 'fas fa-envelope');
                        return;
                    }

                    // Validate hourly rate
                    if (isNaN(hourly_rate) || parseFloat(hourly_rate) < 0) {
                        showPortfolioNotification('Please enter a valid hourly rate', 'error', 'fas fa-dollar-sign');
                        return;
                    }

                    try {
                        // Show loading state
                        saveProfileBtn.disabled = true;
                        saveProfileBtn.textContent = 'Saving...';

                        // Check if we have a profile photo to upload
                        if (selectedFile) {
                            // Use FormData for file upload
                            const formData = new FormData();
                            formData.append('email', email);
                            formData.append('mobile', mobile);
                            formData.append('position', position);
                            formData.append('expertise', expertise);
                            formData.append('hourly_rate', parseFloat(hourly_rate));
                            formData.append('availability', availability);
                            formData.append('country', country);
                            formData.append('language', language);
                            formData.append('profile_photo', selectedFile);

                            const response = await fetch('/update_genius_profile', {
                                method: 'POST',
                                body: formData
                            });

                            const data = await response.json();

                            if (data.success) {
                                // Update all display elements immediately
                                const displayPosition = document.getElementById('displayPosition');
                                const displayCountry = document.getElementById('displayCountry');
                                const profilePosition = document.getElementById('profilePosition');
                                const displayAvailability = document.getElementById('displayAvailability');
                                const displayLanguage = document.getElementById('displayLanguage');
                                const displayCountryField = document.getElementById('displayCountryField');
                                const hourlyRateElement = document.getElementById('hourlyRate');

                                if (displayPosition) displayPosition.textContent = position;
                                if (displayCountry) displayCountry.textContent = country;
                                if (profilePosition) profilePosition.textContent = position;
                                if (displayAvailability) {
                                    const displayText = availability === 'fulltime' ? 'Full-Time' : availability === 'parttime' ? 'Part-Time' : availability;
                                    displayAvailability.value = displayText;
                                }
                                if (displayLanguage) displayLanguage.value = language;
                                if (displayCountryField) displayCountryField.value = country;
                                if (hourlyRateElement) {
                                    if (hourly_rate && hourly_rate > 0) {
                                        hourlyRateElement.textContent = `$${hourly_rate}`;
                                    } else {
                                        hourlyRateElement.textContent = 'No rate set';
                                    }
                                }

                                // Update navigation profile photo if a new photo was uploaded
                                if (selectedFile) {
                                    const timestamp = new Date().getTime();
                                    const navProfilePhoto = document.getElementById('navProfilePhoto');
                                    if (navProfilePhoto) {
                                        navProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                                    }
                                    // Also update the modal photo to show the saved version
                                    const currentProfilePhoto = document.getElementById('currentProfilePhoto');
                                    if (currentProfilePhoto) {
                                        currentProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                                    }
                                }

                                showPortfolioNotification('Profile updated successfully!', 'success', 'fas fa-user-check');
                                closeModal(editProfileModal);
                                // Don't reload the page, just update the display
                            } else {
                                showPortfolioNotification('Error: ' + (data.error || 'Failed to update profile'), 'error', 'fas fa-exclamation-triangle');
                            }
                        } else {
                            // Use JSON for regular data without file upload
                            const response = await fetch('/update_genius_profile', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    email: email,
                                    mobile: mobile,
                                    position: position,
                                    expertise: expertise,
                                    hourly_rate: parseFloat(hourly_rate),
                                    availability: availability,
                                    country: country,
                                    language: language
                                })
                            });

                            const data = await response.json();

                            if (data.success) {
                                // Update all display elements immediately
                                const displayPosition = document.getElementById('displayPosition');
                                const displayCountry = document.getElementById('displayCountry');
                                const profilePosition = document.getElementById('profilePosition');
                                const displayAvailability = document.getElementById('displayAvailability');
                                const displayLanguage = document.getElementById('displayLanguage');
                                const displayCountryField = document.getElementById('displayCountryField');
                                const hourlyRateElement = document.getElementById('hourlyRate');

                                if (displayPosition) displayPosition.textContent = position;
                                if (displayCountry) displayCountry.textContent = country;
                                if (profilePosition) profilePosition.textContent = position;
                                if (displayAvailability) {
                                    const displayText = availability === 'fulltime' ? 'Full-Time' : availability === 'parttime' ? 'Part-Time' : availability;
                                    displayAvailability.value = displayText;
                                }
                                if (displayLanguage) displayLanguage.value = language;
                                if (displayCountryField) displayCountryField.value = country;
                                if (hourlyRateElement) {
                                    if (hourly_rate && hourly_rate > 0) {
                                        hourlyRateElement.textContent = `$${hourly_rate}`;
                                    } else {
                                        hourlyRateElement.textContent = 'No rate set';
                                    }
                                }

                                showPortfolioNotification('Profile updated successfully!', 'success', 'fas fa-user-check');
                                closeModal(editProfileModal);
                                // Don't reload the page, just update the display
                            } else {
                                showPortfolioNotification('Error: ' + (data.error || 'Failed to update profile'), 'error', 'fas fa-exclamation-triangle');
                            }
                        }
                    } catch (error) {
                        showPortfolioNotification('An error occurred while saving your profile', 'error', 'fas fa-exclamation-circle');
                        console.error('Profile update error:', error);
                    } finally {
                        saveProfileBtn.disabled = false;
                        saveProfileBtn.textContent = 'Next';
                    }
                });
            }



            // Portfolio save functionality
            const portfolioAddSaveBtn = document.getElementById('portfolioAddSaveBtn');
            console.log('Portfolio save button found:', portfolioAddSaveBtn);
            if (portfolioAddSaveBtn) {
                portfolioAddSaveBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Portfolio Next: Preview clicked');

                    // Get form values
                    const projectTitle = document.getElementById('projectTitle').value.trim();
                    const projectRole = document.getElementById('projectRole').value.trim();
                    const projectDescription = document.getElementById('portfolioContent').value.trim();
                    const projectContent = document.getElementById('portfolioProjectContent').innerHTML.trim();
                    const projectSkills = document.getElementById('projectSkills').value.trim();
                    const relatedJob = document.getElementById('relatedJob').value.trim();

                    // Validate required fields
                    if (!projectTitle) {
                        showPortfolioNotification('Please enter a project title', 'error', 'fas fa-heading');
                        return;
                    }

                    if (!projectDescription) {
                        showPortfolioNotification('Please enter a project description', 'error', 'fas fa-align-left');
                        return;
                    }

                    try {
                        // Show loading state
                        portfolioAddSaveBtn.disabled = true;
                        portfolioAddSaveBtn.textContent = 'Saving...';

                        console.log('Saving project and publishing - Title:', projectTitle, 'Role:', projectRole, 'Description:', projectDescription);

                        // Send request to save portfolio title as published
                        const response = await fetch('/save_portfolio_title', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                project_title: projectTitle,
                                project_role: projectRole,
                                project_description: projectDescription,
                                project_content: projectContent,
                                skills_and_deliverables: projectSkills,
                                related_giggenius_job: relatedJob,
                                action: 'published'
                            })
                        });

                        console.log('Response status:', response.status);
                        const data = await response.json();
                        console.log('Response data:', data);

                        if (data.success) {
                            // Show success notification with portfolio icon
                            showPortfolioNotification('Project published successfully! Your portfolio has been updated.', 'success', 'fas fa-rocket');

                            // Reset form
                            document.getElementById('projectTitle').value = '';
                            document.getElementById('projectRole').value = '';
                            document.getElementById('projectSkills').value = '';
                            document.getElementById('relatedJob').value = '';
                            document.getElementById('portfolioContent').value = '';
                            document.getElementById('portfolioProjectContent').innerHTML = '';



                            // Navigate to Published section and reload page after notification shows
                            setTimeout(() => {
                                if (data.redirect === 'published') {
                                    // Switch to published tab
                                    const publishedTab = document.querySelector('[data-tab="published"]');
                                    if (publishedTab) {
                                        publishedTab.click();
                                    }
                                }

                                // Reload the page to show updated project title
                                window.location.reload();
                            }, 1500);
                        } else {
                            showPortfolioNotification('Error: ' + (data.error || 'Failed to save and publish project title'), 'error', 'fas fa-exclamation-triangle');
                        }
                    } catch (error) {
                        showPortfolioNotification('An error occurred while saving your project. Please try again.', 'error', 'fas fa-exclamation-circle');
                        console.error('Portfolio save error:', error);
                    } finally {
                        portfolioAddSaveBtn.disabled = false;
                        portfolioAddSaveBtn.textContent = 'Next: Preview';
                    }
                });
            }



            // Portfolio Tabs functionality
            const portfolioTabs = document.querySelectorAll('.portfolio-tab');
            const publishedContent = document.getElementById('publishedContent');
            const draftsContent = document.getElementById('draftsContent');

            portfolioTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    portfolioTabs.forEach(t => t.classList.remove('active'));
                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Show/hide different portfolio content
                    const tabType = this.getAttribute('data-tab');
                    console.log('Switched to tab:', tabType);

                    if (tabType === 'published') {
                        publishedContent.style.display = 'block';
                        draftsContent.style.display = 'none';
                    } else if (tabType === 'drafts') {
                        publishedContent.style.display = 'none';
                        draftsContent.style.display = 'block';
                    }
                });
            });

            // Portfolio Pagination functionality
            const paginationBtns = document.querySelectorAll('.pagination-btn');
            paginationBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    if (!this.classList.contains('prev') && !this.classList.contains('next')) {
                        // Remove active class from all pagination buttons
                        paginationBtns.forEach(b => b.classList.remove('active'));
                        // Add active class to clicked button
                        this.classList.add('active');
                    }
                });
            });

            // Portfolio Draft Save functionality
            if (portfolioAddBackBtn) {
                portfolioAddBackBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Portfolio Save as Draft clicked');

                    // Get form values
                    const projectTitle = document.getElementById('projectTitle').value.trim();
                    const projectRole = document.getElementById('projectRole').value.trim();
                    const projectDescription = document.getElementById('portfolioContent').value.trim();
                    const projectContent = document.getElementById('portfolioProjectContent').innerHTML.trim();
                    const projectSkills = document.getElementById('projectSkills').value.trim();
                    const relatedJob = document.getElementById('relatedJob').value.trim();

                    // Validate required fields (only title required for draft)
                    if (!projectTitle) {
                        showPortfolioNotification('Please enter a project title to save as draft', 'error', 'fas fa-heading');
                        return;
                    }

                    try {
                        // Show loading state
                        portfolioAddBackBtn.disabled = true;
                        portfolioAddBackBtn.textContent = 'Saving...';

                        console.log('Saving project as draft - Title:', projectTitle, 'Role:', projectRole, 'Description:', projectDescription);

                        // Send request to save portfolio title
                        const response = await fetch('/save_portfolio_title', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                project_title: projectTitle,
                                project_role: projectRole,
                                project_description: projectDescription,
                                project_content: projectContent,
                                skills_and_deliverables: projectSkills,
                                related_giggenius_job: relatedJob,
                                action: 'draft'
                            })
                        });

                        console.log('Draft response status:', response.status);
                        const data = await response.json();
                        console.log('Draft response data:', data);

                        if (data.success) {
                            // Show success notification with draft icon
                            showPortfolioNotification('Project saved as draft successfully! You can publish it later.', 'success', 'fas fa-save');

                            // Reset form
                            document.getElementById('projectTitle').value = '';
                            document.getElementById('projectRole').value = '';
                            document.getElementById('projectSkills').value = '';
                            document.getElementById('relatedJob').value = '';
                            document.getElementById('portfolioContent').value = '';
                            document.getElementById('portfolioProjectContent').innerHTML = '';



                            // Navigate to Drafts section and reload page after notification shows
                            setTimeout(() => {
                                if (data.redirect === 'drafts') {
                                    // Switch to drafts tab
                                    const draftsTab = document.querySelector('[data-tab="drafts"]');
                                    if (draftsTab) {
                                        draftsTab.click();
                                    }
                                }

                                // Reload the page to show updated project title
                                window.location.reload();
                            }, 1500);
                        } else {
                            showPortfolioNotification('Error: ' + (data.error || 'Failed to save project title as draft'), 'error', 'fas fa-exclamation-triangle');
                        }
                    } catch (error) {
                        showPortfolioNotification('An error occurred while saving your project as draft. Please try again.', 'error', 'fas fa-exclamation-circle');
                        console.error('Portfolio draft save error:', error);
                    } finally {
                        portfolioAddBackBtn.disabled = false;
                        portfolioAddBackBtn.textContent = 'Save as draft';
                    }
                });
            }

            // Portfolio View/Publish functionality
            const publishPortfolioBtns = document.querySelectorAll('.publish-portfolio-btn');
            publishPortfolioBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const portfolioCard = this.closest('.portfolio-card');
                    const projectTitle = portfolioCard.getAttribute('data-project-title');
                    const projectRole = portfolioCard.getAttribute('data-project-role');
                    const projectDescription = portfolioCard.getAttribute('data-project-description');
                    const projectSkills = portfolioCard.getAttribute('data-project-skills');
                    const projectImage = portfolioCard.getAttribute('data-project-image');

                    // Update modal content
                    const modalTitle = portfolioViewModal.querySelector('.project-title');
                    const modalRole = portfolioViewModal.querySelector('.project-role strong');
                    const modalDescription = portfolioViewModal.querySelector('.project-description');
                    const modalImage = portfolioViewModal.querySelector('#portfolioViewImage');
                    const modalSkillsContainer = portfolioViewModal.querySelector('.skills-tags');

                    if (modalTitle) modalTitle.textContent = projectTitle;
                    if (modalRole) modalRole.textContent = projectRole;
                    if (modalDescription) modalDescription.textContent = projectDescription;
                    if (modalImage) modalImage.src = projectImage;

                    // Update skills
                    if (modalSkillsContainer && projectSkills) {
                        modalSkillsContainer.innerHTML = '';
                        const skills = projectSkills.split(',');
                        skills.forEach(skill => {
                            const skillTag = document.createElement('span');
                            skillTag.className = 'skill-tag';
                            skillTag.style.cssText = 'background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;';
                            skillTag.textContent = skill.trim();
                            modalSkillsContainer.appendChild(skillTag);
                        });
                    }

                    // Open modal
                    portfolioViewModal.style.display = 'flex';
                    portfolioViewModal.classList.add('active');
                    document.body.style.overflow = 'hidden';

                    console.log('Portfolio view opened for:', projectTitle);
                });
            });

            // Portfolio Card Click functionality for modal view
            const clickableCards = document.querySelectorAll('.clickable-card');
            clickableCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    e.preventDefault();

                    const projectId = this.getAttribute('data-project-id');
                    const projectTitle = this.getAttribute('data-project-title');
                    const projectRole = this.getAttribute('data-project-role');
                    const projectDescription = this.getAttribute('data-project-description');
                    const projectContent = this.getAttribute('data-project-content');
                    const projectSkills = this.getAttribute('data-project-skills');
                    const relatedJob = this.getAttribute('data-related-job');
                    const hasImage = this.getAttribute('data-has-image') === 'true';
                    const isPublished = !this.getAttribute('data-status'); // draft has data-status="draft"

                    console.log('Project clicked:', {
                        id: projectId,
                        title: projectTitle,
                        role: projectRole,
                        description: projectDescription,
                        content: projectContent,
                        skills: projectSkills,
                        relatedJob: relatedJob
                    });

                    // Update modal content
                    const modalTitle = portfolioViewModal.querySelector('.project-title');
                    const modalRole = portfolioViewModal.querySelector('.project-role strong');
                    const modalDescription = portfolioViewModal.querySelector('.project-description');
                    const modalImage = portfolioViewModal.querySelector('#portfolioViewImage');
                    const modalSkillsContainer = portfolioViewModal.querySelector('.skills-tags');
                    const contentSection = portfolioViewModal.querySelector('.content-section');
                    const projectContentDiv = portfolioViewModal.querySelector('.project-content');
                    const relatedJobSection = portfolioViewModal.querySelector('.related-job-section');
                    const relatedJobText = portfolioViewModal.querySelector('.related-job-text');

                    if (modalTitle) modalTitle.textContent = projectTitle;
                    if (modalRole) modalRole.textContent = projectRole || 'Not specified';

                    // Handle project description - extract text from HTML if needed
                    if (modalDescription) {
                        if (projectDescription) {
                            // If description contains HTML tags, extract text content only
                            if (projectDescription.includes('<') && projectDescription.includes('>')) {
                                // Create a temporary div to extract text content from HTML
                                const tempDiv = document.createElement('div');
                                tempDiv.innerHTML = projectDescription;
                                const textContent = tempDiv.textContent || tempDiv.innerText || '';
                                modalDescription.textContent = textContent.trim() || 'No description provided';
                            } else {
                                modalDescription.textContent = projectDescription;
                            }
                        } else {
                            modalDescription.textContent = 'No description provided';
                        }
                    }

                    // Project image is no longer displayed in modal since Project Content is now on the right side

                    // Update skills
                    if (modalSkillsContainer && projectSkills) {
                        modalSkillsContainer.innerHTML = '';
                        const skills = projectSkills.split(',');
                        skills.forEach(skill => {
                            const skillTag = document.createElement('span');
                            skillTag.className = 'skill-tag';
                            skillTag.style.cssText = 'background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;';
                            skillTag.textContent = skill.trim();
                            modalSkillsContainer.appendChild(skillTag);
                        });
                    } else if (modalSkillsContainer) {
                        modalSkillsContainer.innerHTML = '<span style="color: #666; font-style: italic;">No skills specified</span>';
                    }

                    // Update project content
                    if (projectContent && projectContent.trim()) {
                        if (contentSection) contentSection.style.display = 'block';
                        if (projectContentDiv) {
                            projectContentDiv.innerHTML = projectContent;

                            // Clean up any unwanted text content after inserting HTML
                            setTimeout(() => {
                                // Remove all text nodes that contain HTML entities or unwanted characters
                                const walker = document.createTreeWalker(
                                    projectContentDiv,
                                    NodeFilter.SHOW_TEXT,
                                    null,
                                    false
                                );

                                const textNodes = [];
                                let node;
                                while (node = walker.nextNode()) {
                                    textNodes.push(node);
                                }

                                textNodes.forEach(textNode => {
                                    const text = textNode.textContent.trim();
                                    // Remove text nodes that contain HTML entities, special characters, or are just whitespace
                                    if (text.includes('\\x') || text.includes('\\n') || text.includes('b\'') ||
                                        text.match(/^[\s\n\r\t]*$/) || text.match(/[^\w\s]/) || text.length < 3) {
                                        textNode.remove();
                                    }
                                });

                                // Also remove any empty elements
                                const emptyElements = projectContentDiv.querySelectorAll('p:empty, span:empty, div:empty, b:empty, strong:empty');
                                emptyElements.forEach(el => el.remove());
                            }, 100);
                        }
                    } else {
                        if (contentSection) contentSection.style.display = 'none';
                    }

                    // Update related job
                    if (relatedJob && relatedJob.trim()) {
                        if (relatedJobSection) relatedJobSection.style.display = 'block';
                        if (relatedJobText) relatedJobText.textContent = relatedJob;
                    } else {
                        if (relatedJobSection) relatedJobSection.style.display = 'none';
                    }

                    // Open modal
                    portfolioViewModal.style.display = 'flex';
                    portfolioViewModal.classList.add('active');
                    document.body.style.overflow = 'hidden';

                    console.log('Portfolio card clicked:', projectTitle);
                });
            });

            // Fetch and display the hourly_rate from the new API route in the Hourly Rate section
            fetch('/api/genius/hourly_rate')
                .then(response => response.json())
                .then(data => {
                    const el = document.getElementById('hourlyRate');
                    if (data.success && data.hourly_rate !== undefined && data.hourly_rate !== null && data.hourly_rate !== '' && data.hourly_rate != 0) {
                        el.textContent = `$${data.hourly_rate}`;
                    } else {
                        el.textContent = 'No rate set';
                    }
                })
                .catch(() => {
                    document.getElementById('hourlyRate').textContent = 'Unavailable';
                });

        // Portfolio Delete Function
        async function deletePortfolio(projectId) {
            console.log('Delete portfolio clicked for project:', projectId);

            // Show confirmation dialog
            if (!confirm('Are you sure you want to delete this portfolio project? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch('/delete_portfolio', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        project_id: projectId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Show success notification
                    showPortfolioNotification('Portfolio project deleted successfully!', 'success', 'fas fa-trash');

                    // Remove the portfolio card from the DOM
                    const portfolioCard = document.querySelector(`[data-project-id="${projectId}"]`);
                    if (portfolioCard) {
                        portfolioCard.style.transition = 'all 0.3s ease';
                        portfolioCard.style.opacity = '0';
                        portfolioCard.style.transform = 'scale(0.8)';

                        setTimeout(() => {
                            portfolioCard.remove();

                            // Check if there are any remaining projects in the current tab
                            const activeTab = document.querySelector('.portfolio-tab.active');
                            const currentTabContent = activeTab.getAttribute('data-tab') === 'published' ?
                                document.getElementById('publishedContent') :
                                document.getElementById('draftsContent');

                            const remainingCards = currentTabContent.querySelectorAll('.portfolio-card');
                            if (remainingCards.length === 0) {
                                const emptyMessage = activeTab.getAttribute('data-tab') === 'published' ?
                                    'No published projects yet. Click + to add your first project.' :
                                    'No draft projects yet.';
                                currentTabContent.innerHTML = `<div style="padding:2rem;text-align:center;color:#888;">${emptyMessage}</div>`;
                            }
                        }, 300);
                    }
                } else {
                    showPortfolioNotification(data.error || 'Failed to delete portfolio project', 'error', 'fas fa-exclamation-triangle');
                }
            } catch (error) {
                console.error('Error deleting portfolio:', error);
                showPortfolioNotification('An error occurred while deleting the portfolio project', 'error', 'fas fa-exclamation-triangle');
            }
        }

        // Portfolio Content Functions
        function addImageUpload() {
            console.log('Image upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.multiple = true; // Allow multiple file selection
            input.style.display = 'none';

            input.onchange = function(e) {
                const files = e.target.files;
                console.log('Files selected:', files.length, 'images');

                if (files.length > 0) {
                    // Process each selected file
                    Array.from(files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log(`Image ${index + 1} loaded, adding content block`);
                            addContentBlock('image', {
                                src: e.target.result,
                                name: file.name,
                                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                            });
                        };
                        reader.readAsDataURL(file);
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addVideoUpload() {
            console.log('Video upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'video/*';
            input.multiple = true; // Allow multiple video selection
            input.style.display = 'none';

            input.onchange = function(e) {
                const files = e.target.files;
                console.log('Video files selected:', files.length, 'videos');

                if (files.length > 0) {
                    Array.from(files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log(`Video ${index + 1} loaded, adding content block`);
                            addContentBlock('video', {
                                src: e.target.result,
                                name: file.name,
                                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                            });
                        };
                        reader.readAsDataURL(file);
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addTextBlock() {
            console.log('Text block button clicked');
            // Create text block modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000; display: flex;
                justify-content: center; align-items: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 8px; padding: 2rem; max-width: 600px; width: 90%;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <h3 style="margin: 0; color: #004AAD;">Add Text Block</h3>
                        <div style="display: flex; gap: 1rem;">
                            <button id="plainTextBtn" style="padding: 0.5rem 1rem; border: 1px solid #004AAD; background: #004AAD; color: white; border-radius: 4px; cursor: pointer;">Plain text</button>
                            <button id="markdownBtn" style="padding: 0.5rem 1rem; border: 1px solid #004AAD; background: white; color: #004AAD; border-radius: 4px; cursor: pointer;">Markdown</button>
                        </div>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; font-weight: 600; margin-bottom: 0.5rem;">Heading</label>
                        <input type="text" id="textHeading" placeholder="Enter heading" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <textarea id="textContent" placeholder="Enter your text" style="width: 100%; height: 200px; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
                    </div>
                    <div style="display: flex; justify-content: flex-end; gap: 1rem;">
                        <button id="cancelTextBtn" style="padding: 0.75rem 1.5rem; border: 1px solid #ddd; background: white; color: #666; border-radius: 4px; cursor: pointer;">Cancel</button>
                        <button id="addTextBtn" style="padding: 0.75rem 1.5rem; border: none; background: #CD208B; color: white; border-radius: 4px; cursor: pointer;">Add Text</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            console.log('Text block modal created and added to DOM');

            // Event listeners
            modal.querySelector('#cancelTextBtn').onclick = () => {
                console.log('Text block cancelled');
                document.body.removeChild(modal);
            };
            modal.querySelector('#addTextBtn').onclick = () => {
                const heading = modal.querySelector('#textHeading').value;
                const content = modal.querySelector('#textContent').value;
                console.log('Adding text block:', { heading, content });
                if (content.trim()) {
                    addContentBlock('text', { heading, content });
                    document.body.removeChild(modal);
                } else {
                    showPortfolioNotification('Please enter some text content', 'error', 'fas fa-edit');
                }
            };
        }

        function addPdfUpload() {
            console.log('PDF upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.pdf';
            input.style.display = 'none';

            input.onchange = function(e) {
                console.log('PDF file selected:', e.target.files[0]);
                const file = e.target.files[0];
                if (file) {
                    console.log('Adding PDF content block');
                    addContentBlock('pdf', {
                        name: file.name,
                        size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addMusic() {
            console.log('Music upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'audio/*';
            input.multiple = true; // Allow multiple audio selection
            input.style.display = 'none';

            input.onchange = function(e) {
                const files = e.target.files;
                console.log('Music files selected:', files.length, 'music files');

                if (files.length > 0) {
                    Array.from(files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log(`Music ${index + 1} loaded, adding content block`);
                            addContentBlock('music', {
                                src: e.target.result,
                                name: file.name,
                                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                            });
                        };
                        reader.readAsDataURL(file);
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addLink() {
            console.log('Add link button clicked');
            // Create link modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000; display: flex;
                justify-content: center; align-items: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 8px; padding: 2rem; max-width: 500px; width: 90%;">
                    <div style="margin-bottom: 1rem;">
                        <h3 style="margin: 0; color: #004AAD;">Add Link</h3>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; font-weight: 600; margin-bottom: 0.5rem;">Link Title</label>
                        <input type="text" id="linkTitle" placeholder="Enter link title" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; font-weight: 600; margin-bottom: 0.5rem;">URL</label>
                        <input type="url" id="linkUrl" placeholder="https://example.com" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="display: flex; justify-content: flex-end; gap: 1rem;">
                        <button id="cancelLinkBtn" style="padding: 0.75rem 1.5rem; border: 1px solid #ddd; background: white; color: #666; border-radius: 4px; cursor: pointer;">Cancel</button>
                        <button id="addLinkBtn" style="padding: 0.75rem 1.5rem; border: none; background: #CD208B; color: white; border-radius: 4px; cursor: pointer;">Add Link</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            console.log('Link modal created and added to DOM');

            // Event listeners
            modal.querySelector('#cancelLinkBtn').onclick = () => {
                console.log('Link cancelled');
                document.body.removeChild(modal);
            };
            modal.querySelector('#addLinkBtn').onclick = () => {
                const title = modal.querySelector('#linkTitle').value;
                const url = modal.querySelector('#linkUrl').value;
                console.log('Adding link:', { title, url });
                if (title.trim() && url.trim()) {
                    addContentBlock('link', { title, url });
                    document.body.removeChild(modal);
                } else {
                    showPortfolioNotification('Please enter both title and URL', 'error', 'fas fa-link');
                }
            };
        }

        function addContentBlock(type, data) {
            const container = document.getElementById('portfolioProjectContent');
            const blockId = 'block_' + Date.now();

            let blockHTML = '';

            switch(type) {
                case 'image':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 0; border: none; border-radius: 4px; position: relative;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px; z-index: 10;">×</button>
                            <img src="${data.src}" alt="" style="width: 100%; height: auto; border-radius: 4px; cursor: pointer; display: block;" onclick="openImagePreview('${data.src}', '')">
                        </div>
                    `;
                    break;
                case 'video':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <video controls style="max-width: 100%; height: auto; border-radius: 4px;">
                                <source src="${data.src}" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    `;
                    break;
                case 'text':
                    blockHTML = `
                        <div class="content-block text-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative; display: none !important;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            ${data.heading ? `<h4 style="margin: 0 0 0.5rem 0; color: #004AAD;">${data.heading}</h4>` : ''}
                            <p style="margin: 0; line-height: 1.6; white-space: pre-wrap;">${data.content}</p>
                        </div>
                    `;
                    break;
                case 'pdf':
                    blockHTML = `
                        <div class="content-block pdf-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative; display: none !important; align-items: center; gap: 1rem;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <i class="fas fa-file-pdf" style="font-size: 2rem; color: #ff4444;"></i>
                            <div>
                                <p style="margin: 0; font-weight: 600;">${data.name}</p>
                                <p style="margin: 0; font-size: 0.9rem; color: #666;">${data.size}</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'music':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <audio controls style="width: 100%;">
                                <source src="${data.src}" type="audio/mpeg">
                                Your browser does not support the audio element.
                            </audio>
                        </div>
                    `;
                    break;
                case 'link':
                    blockHTML = `
                        <div class="content-block link-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative; display: none !important; align-items: center; gap: 1rem;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <i class="fas fa-link" style="font-size: 1.5rem; color: #004AAD;"></i>
                            <div style="flex: 1;">
                                <a href="${data.url}" target="_blank" style="color: #004AAD; text-decoration: none; font-weight: 600; display: block;">${data.title}</a>
                                <p style="margin: 0; font-size: 0.9rem; color: #666; word-break: break-all;">${data.url}</p>
                            </div>
                        </div>
                    `;
                    break;
            }

            container.insertAdjacentHTML('beforeend', blockHTML);
        }

        function removeBlock(blockId) {
            const block = document.getElementById(blockId);
            if (block) {
                block.remove();
            }
        }

        function openImagePreview(src, name) {
            console.log('Opening image preview for:', name);
            // Create image preview modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
                justify-content: center; align-items: center; cursor: pointer;
            `;

            modal.innerHTML = `
                <div style="max-width: 90%; max-height: 90%; position: relative;">
                    <button onclick="this.parentElement.parentElement.remove()" style="position: absolute; top: -40px; right: 0; background: white; color: #333; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-size: 16px;">×</button>
                    <img src="${src}" alt="" style="max-width: 100%; max-height: 100%; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                </div>
            `;

            // Close modal when clicking outside the image
            modal.onclick = function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            };

            document.body.appendChild(modal);
        }

        // Setup portfolio button event listeners
        function setupPortfolioButtons() {
            console.log('Setting up portfolio button listeners');
            const imageBtn = document.getElementById('uploadImageBtn');
            const videoBtn = document.getElementById('portfolioUploadVideoBtn');
            const textBtn = document.getElementById('addTextBlockBtn');
            const linkBtn = document.getElementById('addLinkBtn');
            const pdfBtn = document.getElementById('uploadPdfBtn');
            const musicBtn = document.getElementById('addMusicBtn');

            console.log('Image button found:', imageBtn);
            console.log('Video button found:', videoBtn);
            console.log('Text button found:', textBtn);
            console.log('Link button found:', linkBtn);
            console.log('PDF button found:', pdfBtn);
            console.log('Music button found:', musicBtn);

            if (imageBtn) {
                imageBtn.removeEventListener('click', addImageUpload); // Remove existing listener
                imageBtn.addEventListener('click', addImageUpload);
            }
            if (videoBtn) {
                videoBtn.removeEventListener('click', addVideoUpload);
                videoBtn.addEventListener('click', addVideoUpload);
            }
            if (textBtn) {
                textBtn.removeEventListener('click', addTextBlock);
                textBtn.addEventListener('click', addTextBlock);
            }
            if (linkBtn) {
                linkBtn.removeEventListener('click', addLink);
                linkBtn.addEventListener('click', addLink);
            }
            if (pdfBtn) {
                pdfBtn.removeEventListener('click', addPdfUpload);
                pdfBtn.addEventListener('click', addPdfUpload);
            }
            if (musicBtn) {
                musicBtn.removeEventListener('click', addMusic);
                musicBtn.addEventListener('click', addMusic);
            }
        }

        });

        // Header functionality
        document.addEventListener('DOMContentLoaded', function() {

            // Profile dropdown functionality
            const profileButton = document.querySelector('.profile-button');
            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileDropdownContent = document.querySelector('.profile-dropdown-content');

            if (profileButton && profileDropdown && profileDropdownContent) {
                profileButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    profileDropdownContent.classList.toggle('active');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!profileDropdown.contains(e.target)) {
                        profileDropdownContent.classList.remove('active');
                    }
                });

                // Close dropdown when pressing Escape
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape') {
                        profileDropdownContent.classList.remove('active');
                        profileButton.focus();
                    }
                });

                // Keyboard navigation within dropdown
                profileDropdownContent.addEventListener('keydown', function(e) {
                    const links = Array.from(profileDropdownContent.querySelectorAll('a'));
                    const currentIndex = links.indexOf(document.activeElement);

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        const nextIndex = (currentIndex + 1) % links.length;
                        links[nextIndex].focus();
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        const prevIndex = (currentIndex - 1 + links.length) % links.length;
                        links[prevIndex].focus();
                    } else if (e.key === 'Escape') {
                        e.preventDefault();
                        profileDropdownContent.classList.remove('active');
                        profileButton.focus();
                    }
                });
            }

            // Notification functionality
            const notificationBell = document.getElementById('notification-bell');
            const notificationDropdown = document.querySelector('.notification-dropdown');

            if (notificationBell && notificationDropdown) {
                notificationBell.addEventListener('click', function(e) {
                    e.stopPropagation();
                    notificationDropdown.classList.toggle('active');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!notificationDropdown.contains(e.target) && !notificationBell.contains(e.target)) {
                        notificationDropdown.classList.remove('active');
                    }
                });
            }

            // Search functionality
            function performSearch() {
                const searchInput = document.getElementById('searchInput');
                if (searchInput && searchInput.value.trim()) {
                    console.log('Searching for:', searchInput.value);
                    // Add search functionality here
                }
            }

            // Make performSearch available globally
            window.performSearch = performSearch;

            // NEW PORTFOLIO SYSTEM FUNCTIONS

            // Enhanced Open Add Portfolio Modal
            window.openAddPortfolioModal = function() {
                const modal = document.getElementById('addPortfolioModal');
                if (modal) {
                    modal.style.display = 'block';
                    document.body.style.overflow = 'hidden'; // Prevent background scrolling

                    // Reset form
                    const form = document.getElementById('portfolioForm');
                    form.reset();

                    // Set default status to published
                    const publishedRadio = form.querySelector('input[name="status"][value="published"]');
                    if (publishedRadio) {
                        publishedRadio.checked = true;
                    }

                    // Focus on first input
                    setTimeout(() => {
                        const firstInput = form.querySelector('input[name="title"]');
                        if (firstInput) {
                            firstInput.focus();
                        }
                    }, 100);
                }
            };

            // Enhanced Close Add Portfolio Modal
            window.closeAddPortfolioModal = function() {
                const modal = document.getElementById('addPortfolioModal');
                if (modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto'; // Restore scrolling
                }
            };

            // Close modal when clicking outside
            window.addEventListener('click', function(event) {
                const modal = document.getElementById('addPortfolioModal');
                if (event.target === modal) {
                    closeAddPortfolioModal();
                }
            });

            // Close modal with Escape key
            window.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    const modal = document.getElementById('addPortfolioModal');
                    if (modal && modal.style.display === 'block') {
                        closeAddPortfolioModal();
                    }
                }
            });

            // Enhanced Save Portfolio with Validation
            window.savePortfolio = async function() {
                const form = document.getElementById('portfolioForm');
                const formData = new FormData(form);

                // Get form values
                const title = formData.get('title')?.trim();
                const projectUrl = formData.get('project_url')?.trim();
                const description = formData.get('description')?.trim();
                const technologies = formData.get('technologies')?.trim();
                const projectType = formData.get('project_type');
                const status = formData.get('status') || 'published';

                // Enhanced validation
                const errors = [];

                if (!title) {
                    errors.push('Project title is required');
                }

                if (!projectUrl) {
                    errors.push('Project URL is required');
                } else {
                    // Validate URL format
                    try {
                        new URL(projectUrl.startsWith('http') ? projectUrl : 'https://' + projectUrl);
                    } catch {
                        errors.push('Please enter a valid URL');
                    }
                }

                if (errors.length > 0) {
                    showPortfolioNotification(errors.join('. '), 'error', 'fas fa-exclamation-triangle');
                    return;
                }

                // Show loading state
                const saveButton = document.querySelector('button[onclick="savePortfolio()"]');
                const originalText = saveButton.innerHTML;
                saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                saveButton.disabled = true;

                const portfolioData = {
                    title,
                    description,
                    project_url: projectUrl,
                    technologies,
                    project_type: projectType,
                    status
                };

                try {
                    const response = await fetch('/api/portfolios', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(portfolioData)
                    });

                    const data = await response.json();

                    if (data.success) {
                        showPortfolioNotification(
                            `Portfolio ${status === 'published' ? 'published' : 'saved as draft'} successfully! 🎉`,
                            'success',
                            'fas fa-check-circle'
                        );
                        closeAddPortfolioModal();
                        // Reload page to show new portfolio
                        setTimeout(() => window.location.reload(), 1500);
                    } else {
                        showPortfolioNotification('Error: ' + data.error, 'error', 'fas fa-exclamation-triangle');
                        // Restore button
                        saveButton.innerHTML = originalText;
                        saveButton.disabled = false;
                    }
                } catch (error) {
                    showPortfolioNotification('Network error. Please check your connection and try again.', 'error', 'fas fa-wifi');
                    console.error('Error saving portfolio:', error);
                    // Restore button
                    saveButton.innerHTML = originalText;
                    saveButton.disabled = false;
                }
            };

            // Delete Portfolio (Updated for new system)
            window.deletePortfolio = async function(portfolioId) {
                console.log('🗑️ Delete portfolio clicked for ID:', portfolioId);

                if (!confirm('Are you sure you want to delete this portfolio project? This action cannot be undone.')) {
                    return;
                }

                try {
                    const response = await fetch(`/api/portfolios/${portfolioId}`, {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    const data = await response.json();

                    if (data.success) {
                        showPortfolioNotification('Portfolio deleted successfully!', 'success', 'fas fa-trash');
                        // Reload page to update portfolio list
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showPortfolioNotification('Error: ' + data.error, 'error', 'fas fa-exclamation-triangle');
                    }
                } catch (error) {
                    showPortfolioNotification('An error occurred while deleting', 'error', 'fas fa-exclamation-circle');
                    console.error('Error deleting portfolio:', error);
                }
            };

            // Edit Portfolio
            window.editPortfolio = function(portfolioId) {
                console.log('✏️ Edit portfolio clicked for ID:', portfolioId);
                // TODO: Implement edit functionality
                showPortfolioNotification('Edit functionality coming soon!', 'info', 'fas fa-info-circle');
            };

            // Enhanced Portfolio Notification Function
            window.showPortfolioNotification = function(message, type = 'success', icon = 'fas fa-check') {
                // Remove any existing notifications
                const existingNotifications = document.querySelectorAll('.portfolio-notification');
                existingNotifications.forEach(notif => notif.remove());

                // Create notification element
                const notification = document.createElement('div');
                notification.className = 'portfolio-notification';

                const bgColor = type === 'success' ? 'linear-gradient(135deg, #28a745, #20c997)' :
                               type === 'error' ? 'linear-gradient(135deg, #dc3545, #e74c3c)' :
                               'linear-gradient(135deg, #17a2b8, #20c997)';

                notification.style.cssText = `
                    position: fixed;
                    bottom: 30px;
                    right: 30px;
                    background: ${bgColor};
                    color: white;
                    padding: 1.25rem 1.75rem;
                    border-radius: 12px;
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                    z-index: 10001;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-weight: 500;
                    font-family: 'Poppins', sans-serif;
                    max-width: 450px;
                    min-width: 300px;
                    animation: portfolioNotificationSlideIn 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.2);
                `;

                notification.innerHTML = `
                    <div style="background: rgba(255,255,255,0.2); padding: 0.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; min-width: 32px; height: 32px;">
                        <i class="${icon}" style="font-size: 1rem;"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="font-size: 0.95rem; line-height: 1.4;">${message}</div>
                    </div>
                    <button onclick="this.parentElement.remove()" style="background: rgba(255,255,255,0.2); border: none; color: white; width: 24px; height: 24px; border-radius: 50%; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 0.8rem; transition: background 0.2s;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        <i class="fas fa-times"></i>
                    </button>
                `;

                // Add animation styles if not already added
                if (!document.querySelector('#portfolio-notification-styles')) {
                    const style = document.createElement('style');
                    style.id = 'portfolio-notification-styles';
                    style.textContent = `
                        @keyframes portfolioNotificationSlideIn {
                            from {
                                transform: translateX(100%) scale(0.8);
                                opacity: 0;
                            }
                            to {
                                transform: translateX(0) scale(1);
                                opacity: 1;
                            }
                        }
                        @keyframes portfolioNotificationSlideOut {
                            from {
                                transform: translateX(0) scale(1);
                                opacity: 1;
                            }
                            to {
                                transform: translateX(100%) scale(0.8);
                                opacity: 0;
                            }
                        }
                        .portfolio-notification:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 12px 35px rgba(0,0,0,0.2);
                        }
                    `;
                    document.head.appendChild(style);
                }

                // Add to page
                document.body.appendChild(notification);

                // Auto remove after 4 seconds
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.style.animation = 'portfolioNotificationSlideOut 0.3s ease-in';
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.parentNode.removeChild(notification);
                            }
                        }, 300);
                    }
                }, 4000);
            };

        });
    </script>

    <!-- Portfolio Modal - Same Style as Edit Profile -->
    <div id="addPortfolioModal" class="modal" style="display: none; position: fixed; z-index: 10000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.4);">
        <div class="modal-content" style="background-color: #ffffff; margin: 2% auto; padding: 0; border: none; border-radius: 12px; width: 95%; max-width: 800px; box-shadow: 0 4px 20px rgba(0,0,0,0.15); max-height: 90vh; overflow-y: auto;">
            <!-- Header with Logo - Exact Same as Edit Profile -->
            <div style="background: #ffffff; color: #333; padding: 2rem; border-radius: 12px 12px 0 0; position: relative; border-bottom: 1px solid #e9ecef;">
                <!-- Logo - Using Actual Logo Image -->
                <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                    <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo" style="width: 50px; height: 50px; object-fit: contain;">
                    <div>
                        <h1 style="margin: 0; font-size: 1.5rem; font-weight: 600; font-family: 'Poppins', sans-serif; color: #FF69B4;">
                            GigGenius
                        </h1>
                    </div>
                </div>

                <!-- Welcome Text - Same Style -->
                <h2 style="margin: 0 0 0.5rem 0; font-size: 1.5rem; font-weight: 600; font-family: 'Poppins', sans-serif; color: #333;">Welcome {{ genius.first_name }} {{ genius.last_name }}</h2>

                <!-- Section Title - Same as Edit Profile -->
                <h3 style="margin: 0; font-size: 1.2rem; font-weight: 600; font-family: 'Poppins', sans-serif; color: #333;">Add your portfolio</h3>

                <button onclick="closeAddPortfolioModal()" style="position: absolute; top: 1.5rem; right: 1.5rem; background: #f8f9fa; border: 1px solid #ddd; color: #666; font-size: 1.2rem; cursor: pointer; width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center;" onmouseover="this.style.background='#e9ecef'" onmouseout="this.style.background='#f8f9fa'">
                    ×
                </button>
            </div>

            <!-- Modal Body - Same Style as Edit Profile -->
            <div style="padding: 2.5rem;">
                <form id="portfolioForm">
                    <!-- Two Column Layout -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                        <!-- Project Title -->
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif;">PROJECT TITLE</label>
                            <input type="text" name="title" required style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem; font-family: 'Poppins', sans-serif; background: #f8f9fa;" placeholder="Enter project title" onfocus="this.style.borderColor='#004AAD'; this.style.background='#ffffff'" onblur="this.style.borderColor='#ddd'; this.style.background='#f8f9fa'">
                        </div>

                        <!-- Project URL -->
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif;">PROJECT URL</label>
                            <input type="url" name="project_url" required style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem; font-family: 'Poppins', sans-serif; background: #f8f9fa;" placeholder="https://your-project.com" onfocus="this.style.borderColor='#004AAD'; this.style.background='#ffffff'" onblur="this.style.borderColor='#ddd'; this.style.background='#f8f9fa'">
                        </div>
                    </div>

                    <!-- Full Width Project Description -->
                    <div style="margin-bottom: 2rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif;">PROJECT DESCRIPTION</label>
                        <textarea name="description" rows="4" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem; font-family: 'Poppins', sans-serif; resize: vertical; background: #f8f9fa; line-height: 1.5;" placeholder="Describe your project, the challenges you solved, and the results achieved..." onfocus="this.style.borderColor='#004AAD'; this.style.background='#ffffff'" onblur="this.style.borderColor='#ddd'; this.style.background='#f8f9fa'"></textarea>
                    </div>

                    <!-- Two Column Layout for Category and Technologies -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                        <!-- Project Category -->
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif;">PROJECT CATEGORY</label>
                            <select name="project_type" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem; font-family: 'Poppins', sans-serif; cursor: pointer; background: #f8f9fa;" onfocus="this.style.borderColor='#004AAD'; this.style.background='#ffffff'" onblur="this.style.borderColor='#ddd'; this.style.background='#f8f9fa'">
                                <option value="web_development">Web Development</option>
                                <option value="mobile_app">Mobile App</option>
                                <option value="design">Design</option>
                                <option value="data_science">Data Science</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <!-- Technologies -->
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif;">TECHNOLOGIES USED</label>
                            <input type="text" name="technologies" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem; font-family: 'Poppins', sans-serif; background: #f8f9fa;" placeholder="React, Node.js, MongoDB, etc." onfocus="this.style.borderColor='#004AAD'; this.style.background='#ffffff'" onblur="this.style.borderColor='#ddd'; this.style.background='#f8f9fa'">
                        </div>
                    </div>

                    <!-- Status Selection - Same Style as Edit Profile -->
                    <div style="margin-bottom: 2rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333; font-family: 'Poppins', sans-serif;">STATUS</label>
                        <div style="display: flex; gap: 2rem;">
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input type="radio" name="status" value="published" checked style="accent-color: #004AAD;">
                                <span style="font-family: 'Poppins', sans-serif; color: #333;">Publish Now</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input type="radio" name="status" value="draft" style="accent-color: #004AAD;">
                                <span style="font-family: 'Poppins', sans-serif; color: #333;">Save as Draft</span>
                            </label>
                        </div>
                    </div>

                    <!-- Action Buttons - Same Style as Edit Profile -->
                    <div style="display: flex; gap: 1rem; justify-content: flex-end; padding-top: 2rem; border-top: 1px solid #e9ecef;">
                        <button type="button" onclick="closeAddPortfolioModal()" style="background: #f8f9fa; color: #6c757d; border: 1px solid #ddd; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer; font-size: 1rem; font-weight: 500; font-family: 'Poppins', sans-serif;">
                            Cancel
                        </button>
                        <button type="button" onclick="savePortfolio()" style="background: #004AAD; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer; font-size: 1rem; font-weight: 500; font-family: 'Poppins', sans-serif;" onmouseover="this.style.background='#003a8c'" onmouseout="this.style.background='#004AAD'">
                            Save Project
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="delete-modal" style="display: none;">
        <div class="delete-modal-content">
            <div class="delete-modal-header">
                <h3>
                    <i class="fas fa-exclamation-triangle"></i>
                    Delete Video
                </h3>
            </div>
            <div class="delete-modal-body">
                <p>Are you sure you want to delete your profile video? This action cannot be undone.</p>
            </div>
            <div class="delete-modal-actions">
                <button class="delete-modal-btn cancel" onclick="closeDeleteModal()">Cancel</button>
                <button class="delete-modal-btn confirm" onclick="confirmDeleteVideo()">Delete</button>
            </div>
        </div>
    </div>

    <!-- Notification Modal -->
    <div id="notificationModal" class="notification-modal" style="display: none;">
        <div class="notification-modal-content">
            <div class="notification-modal-header">
                <div class="notification-modal-icon" id="notificationIcon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="notification-modal-title" id="notificationTitle">Success</h3>
            </div>
            <div class="notification-modal-message" id="notificationMessage">
                Operation completed successfully!
            </div>
            <button class="notification-modal-btn" onclick="closeNotificationModal()">OK</button>
        </div>
    </div>

    <!-- Simplified Modal Styles -->
    <style>
        .modal input:focus, .modal textarea:focus, .modal select:focus {
            outline: none;
            border-color: #004AAD;
        }

        .modal input[type="radio"]:checked + div {
            color: #004AAD;
        }
    </style>

</body>
</html>



